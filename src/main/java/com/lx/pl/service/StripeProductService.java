package com.lx.pl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.stripe.dto.StripeItem;

import java.util.List;

public interface StripeProductService extends IService<StripeProduct> {
    // 根据Stripe 生成的产品 ID 获取 StripeProduct 对象
    StripeProduct getStripeProductByStripeProductId(String stripeProductId);

    // 根据planLevel productType priceInterval 查询StripeProduct 对象
    StripeProduct getStripeProductByLevelTypeInterval(String planLevel, String productType, String priceInterval);

    // 根据priceId 查询StripeProduct 对象
    StripeProduct getStripeProductByPriceId(String priceId);

    StripeProduct getStripeProductByBuyItemDto(StripeItem stripeItem);

    List<StripeProduct> getOneTimeProductListByLumenQty(List<Integer> lumenQtyList);
}