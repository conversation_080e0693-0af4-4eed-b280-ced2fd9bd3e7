package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.entity.DailyTaskLumen;
import com.lx.pl.db.mysql.community.entity.LumenRewardLog;
import com.lx.pl.db.mysql.community.entity.TaskLumen;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.db.mysql.gen.repository.LumenRewardLogRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.TaskLumenRsult;
import com.lx.pl.dto.TaskLumenStandards;
import com.lx.pl.enums.TaskGroup;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import com.lx.pl.vo.WatchADTaskDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LumenTaskService {

    Map<String, TaskLumenStandards> taskLumenStandardsMap = new HashMap<>();
    private static final String TASK_LUMEN_STANDARDS = "task_lumen_standards";

    @Autowired
    RedisService redisService;

    @Autowired
    TaskLumenService taskLumenService;

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    PayLumenRecordService payLumenRecordService;

    @Autowired
    LumenRewardLogRepository lumenRewardLogRepository;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    UserMapper userMapper;
    @Autowired
    private LumenService lumenService;

    public List<TaskLumenStandards> getLumenTaskStandards(User user) throws JsonProcessingException {
        List<TaskLumenStandards> resultList = new ArrayList<>();

        String resultTasks = (String) redisService.get(TASK_LUMEN_STANDARDS);

        //缓存中不为空，则直接返回数据
        if (StringUtils.isNotBlank(resultTasks)) {
            resultList = JsonUtils.writeToList(resultTasks, TaskLumenStandards.class);
            return resultList;
        }

        // Redis 无数据，从枚举加载
        resultList = Arrays.stream(com.lx.pl.enums.TaskLumenStandards.values())
                .map(task -> new TaskLumenStandards(task.getTaskId(), task.getTaskName(), task.getTargetCount(), task.getReward(),
                        task.getDescription(), task.getTaskGroupId(), task.getOrder()))
                .collect(Collectors.toList());

        // 缓存到 Redis
        redisService.set(TASK_LUMEN_STANDARDS, JsonUtils.writeToString(resultList));

        return resultList;
    }

    public TaskLumenStandards getLumenTaskStandardsMap(String taskId, User user) throws JsonProcessingException {
        // 如果 map 为空，尝试从 Redis 或 Enum 加载
        if (taskLumenStandardsMap.isEmpty()) {
            List<TaskLumenStandards> taskLumenStandardsList = getLumenTaskStandards(user);
            taskLumenStandardsMap = taskLumenStandardsList.stream()
                    .collect(Collectors.toMap(TaskLumenStandards::getTaskId, task -> task));
        }

        // 直接从 map 取值
        return taskLumenStandardsMap.getOrDefault(taskId, null);
    }

    public Boolean resetTask(User user) {

        //获取用户完成信息
        TaskLumen taskLumen = taskLumenService.getTaskLumenByUserId(user);
        //获取用户每日完成信息
        DailyTaskLumen dailyTaskLumen = taskLumenService.getDailyTaskLumenByUserId(user);


//        //如果已经领取过，则提示不能领取
//        if (dailyTaskLumen.getRewardTaskIds().contains(com.lx.pl.enums.TaskLumenStandards.FreeLumens.getTaskId())) {
//            throw new BadRequestException("Please do not receive it repeatedly");
//        }

//        //每日用户自动领取lumen数量
//        mongoTemplate.updateFirst(
//                new Query(Criteria.where("userId").is(user.getId())),
//                new Update()
//                        .inc("signInNums", 1)
//                        .addToSet("finishTaskIds", "1"),
//                DailyTaskLumen.class
//        );

//        //记录领取日志
//        addLumenRewardLog(user, com.lx.pl.enums.TaskLumenStandards.FreeLumens.getTaskId(),
//                com.lx.pl.enums.TaskLumenStandards.FreeLumens.getTaskName(), com.lx.pl.enums.TaskLumenStandards.FreeLumens.getReward());

        return (!Objects.isNull(taskLumen) && !Objects.isNull(dailyTaskLumen)) ? Boolean.TRUE : Boolean.FALSE;
    }

    public List<TaskLumenRsult> getLumenTask(User user) throws JsonProcessingException {
        List<TaskLumenRsult> rsultList = new ArrayList<>();

        //获取任务标准
        List<TaskLumenStandards> taskLumenStandardsList = getLumenTaskStandards(user);
        //获取用户完成信息
        TaskLumen taskLumen = taskLumenService.getTaskLumenByUserId(user);
        //获取用户每日完成信息
        DailyTaskLumen dailyTaskLumen = taskLumenService.getDailyTaskLumenByUserId(user);
        if (!CollectionUtils.isEmpty(taskLumenStandardsList) && !Objects.isNull(taskLumen)) {
            Map<String, Integer> taskLumenMap = getTaskLumenMap(taskLumen, dailyTaskLumen);
            Set<String> finishTaskIds = CollectionUtils.isEmpty(taskLumen.getFinishTaskIds()) ? new HashSet<>() : taskLumen.getFinishTaskIds();
            Set<String> rewardTaskIds = CollectionUtils.isEmpty(taskLumen.getRewardTaskIds()) ? new HashSet<>() : taskLumen.getRewardTaskIds();
            Set<String> dailyFinishTaskIds = CollectionUtils.isEmpty(dailyTaskLumen.getFinishTaskIds()) ? new HashSet<>() : dailyTaskLumen.getFinishTaskIds();
            Set<String> dailyRewardTaskIds = CollectionUtils.isEmpty(dailyTaskLumen.getRewardTaskIds()) ? new HashSet<>() : dailyTaskLumen.getRewardTaskIds();

            //添加每日任务是否领取集合
            rewardTaskIds.addAll(dailyRewardTaskIds);

            Boolean finishTaskIdChanged = Boolean.FALSE;
            TaskLumenRsult nextWatchADTask = null;
            for (TaskLumenStandards taskLumenStandards : taskLumenStandardsList) {

                TaskLumenRsult taskLumenRsult = new TaskLumenRsult();
                taskLumenRsult.setTaskId(taskLumenStandards.getTaskId());
                taskLumenRsult.setTaskGroupId(taskLumenStandards.getTaskGroupId());
                taskLumenRsult.setOrder(taskLumenStandards.getOrder());
                taskLumenRsult.setTaskName(taskLumenStandards.getTaskName());
                taskLumenRsult.setTargetCount(taskLumenStandards.getTargetCount());
                taskLumenRsult.setReward(taskLumenStandards.getReward());
                taskLumenRsult.setDescription(taskLumenStandards.getDescription());
                taskLumenRsult.setFinishCount(taskLumenMap.get(taskLumenStandards.getTaskId()));
                taskLumenRsult.setBeReward(rewardTaskIds.contains(taskLumenRsult.getTaskId()) ? Boolean.TRUE : Boolean.FALSE);

                if (finishTaskIds.contains(taskLumenRsult.getTaskId()) || dailyFinishTaskIds.contains(taskLumenRsult.getTaskId())) {
                    taskLumenRsult.setBeFinish(Boolean.TRUE);
                } else {
                    //对比已经完成的数量和目标数据进行对比
                    if (taskLumenMap.containsKey(taskLumenStandards.getTaskId()) &&
                            (taskLumenMap.get(taskLumenStandards.getTaskId()) >= taskLumenStandards.getTargetCount())) {
                        taskLumenRsult.setBeFinish(Boolean.TRUE);
                        finishTaskIdChanged = Boolean.TRUE;

                        //判断是每日任务还是长久任务
                        if (Objects.equals(taskLumenRsult.getTaskGroupId(), TaskGroup.ACHIEVEMENT.getId())) {
                            finishTaskIds.add(taskLumenRsult.getTaskId());
                        } else {
                            dailyFinishTaskIds.add(taskLumenRsult.getTaskId());
                        }
                    } else {
                        taskLumenRsult.setBeFinish(Boolean.FALSE);
                        if (com.lx.pl.enums.TaskLumenStandards.WATCH_AD_TASK_IDS_SET.contains(taskLumenRsult.getTaskId())) {
                            //观看广告任务
                            if (nextWatchADTask == null || taskLumenRsult.getTargetCount() < nextWatchADTask.getTargetCount()) {
                                nextWatchADTask = taskLumenRsult;
                            }
                        }
                    }
                }
                rsultList.add(taskLumenRsult);
            }

            dealNextFinishTime(nextWatchADTask, dailyTaskLumen);

            //如果有完成的任务变动，则更新对应的数据
            if (finishTaskIdChanged) {
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(user.getId())),
                        new Update().set("finishTaskIds", finishTaskIds),
                        TaskLumen.class
                );
                //更新每日任务完成数据
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(user.getId())),
                        new Update().set("finishTaskIds", dailyFinishTaskIds),
                        DailyTaskLumen.class
                );
            }
        }
        return rsultList;
    }

    private void dealNextFinishTime(TaskLumenRsult nextWatchADTask, DailyTaskLumen dailyTaskLumen) {
        if (nextWatchADTask == null || nextWatchADTask.getTargetCount() == 1) {
            return;
        }
        if (dailyTaskLumen.getLastWatchADTime() == null) {
            //未看过广告
            nextWatchADTask.setNextFinishTime(String.valueOf(Instant.now().getEpochSecond()));
        } else {
            nextWatchADTask.setNextFinishTime(String.valueOf(dailyTaskLumen.getLastWatchADTime() + LogicConstants.WATCH_AD_INTERVAL_TIME));
        }
    }

    public Map<String, Integer> getTaskLumenMap(TaskLumen taskLumen, DailyTaskLumen dailyTaskLumen) {
        Map<String, Integer> taskLumenMap = new HashMap<>();
        taskLumenMap.put("1", dailyTaskLumen.getSignInNums());
        taskLumenMap.put("2", dailyTaskLumen.getDailyLikeNums());
        taskLumenMap.put("3", dailyTaskLumen.getDailyCommentNums());
        taskLumenMap.put("4", dailyTaskLumen.getDailyShareNums());

        taskLumenMap.put("18", dailyTaskLumen.getDailyWatchADNums());
        taskLumenMap.put("19", dailyTaskLumen.getDailyWatchADNums());
        taskLumenMap.put("20", dailyTaskLumen.getDailyWatchADNums());
        taskLumenMap.put("21", dailyTaskLumen.getDailyWatchADNums());
        taskLumenMap.put("22", dailyTaskLumen.getDailyWatchADNums());

        taskLumenMap.put("5", taskLumen.getCreationNums());
        taskLumenMap.put("6", taskLumen.getFollowIG());
        taskLumenMap.put("7", taskLumen.getFollowX());

        taskLumenMap.put("8", taskLumen.getReceiveLikeNums());
        taskLumenMap.put("9", taskLumen.getReceiveLikeNums());
        taskLumenMap.put("10", taskLumen.getReceiveLikeNums());
        taskLumenMap.put("11", taskLumen.getReceiveLikeNums());

        taskLumenMap.put("12", taskLumen.getReceiveFollowNums());
        taskLumenMap.put("13", taskLumen.getReceiveFollowNums());
        taskLumenMap.put("14", taskLumen.getReceiveFollowNums());

        taskLumenMap.put("15", taskLumen.getSubscribeYoutube());
        taskLumenMap.put("16", taskLumen.getJoinDiscord());
        taskLumenMap.put("17", taskLumen.getWriteReview());
        taskLumenMap.put("23", taskLumen.getWriteReviewProducthunt());

        return taskLumenMap;
    }

    public Boolean finishTasks(String taskId, User user) {
        try {
            Set<String> set = new HashSet<>(List.of("4", "6", "7", "15", "16"));
            if (StringUtils.isBlank(taskId) || !set.contains(taskId)) {
                return Boolean.FALSE;
            }

            switch (taskId) {
                case "4":
                    mongoTemplate.updateFirst(
                            new Query(Criteria.where("userId").is(user.getId())),
                            new Update().inc("dailyShareNums", 1),
                            DailyTaskLumen.class
                    );
                    break;
                case "6":
                    mongoTemplate.updateFirst(
                            new Query(Criteria.where("userId").is(user.getId())),
                            new Update().inc("followIG", 1),
                            TaskLumen.class
                    );
                    break;
                case "7":
                    mongoTemplate.updateFirst(
                            new Query(Criteria.where("userId").is(user.getId())),
                            new Update().inc("followX", 1),
                            TaskLumen.class
                    );
                    break;
                case "15":
                    mongoTemplate.updateFirst(
                            new Query(Criteria.where("userId").is(user.getId())),
                            new Update().inc("subscribeYoutube", 1),
                            TaskLumen.class
                    );
                    break;
                case "16":
                    mongoTemplate.updateFirst(
                            new Query(Criteria.where("userId").is(user.getId())),
                            new Update().inc("joinDiscord", 1),
                            TaskLumen.class
                    );
                    break;
            }

        } catch (Exception e) {
            log.error("用户：{}，完成任务：{}，失败", user.getId(), taskId, e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean receiveTaskReward(List<String> taskIds, User user) {

        //校验是否有重复的任务id
        if (taskIds.size() != new HashSet<>(taskIds).size()) {
            throw new BadRequestException("There are duplicate data in the parameter");
        }
        RLock lock = redissonClient.getLock("receiveTask:" + user.getLoginName());
        try {
            lock.lock();
            //获取用户完成信息
            TaskLumen taskLumen = taskLumenService.getTaskLumenByUserId(user);
            //获取用户每日完成信息
            DailyTaskLumen dailyTaskLumen = taskLumenService.getDailyTaskLumenByUserId(user);

            Set<String> rewardTaskIds = new HashSet<>();
            if (!CollectionUtils.isEmpty(taskLumen.getRewardTaskIds())) {
                rewardTaskIds.addAll(taskLumen.getRewardTaskIds());
            }
            if (!CollectionUtils.isEmpty(dailyTaskLumen.getRewardTaskIds())) {
                rewardTaskIds.addAll(dailyTaskLumen.getRewardTaskIds());
            }

            for (String taskId : taskIds) {
                //如果已经领取过，则提示不能领取
                if (rewardTaskIds.contains(taskId)) {
                    if (com.lx.pl.enums.TaskLumenStandards.WATCH_AD_TASK_IDS_SET.contains(taskId)) {
                        continue;
                    }
                    throw new BadRequestException("Please do not receive it repeatedly");
                }

                dealReceiveTaskReward(taskId, taskLumen, dailyTaskLumen, user);
            }
        } catch (Exception e) {
            log.error("用户【{}】领取任务奖励报错, ", user.getLoginName(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }



        return Boolean.TRUE;
    }

    public Boolean dealReceiveTaskReward(String taskId, TaskLumen taskLumen, DailyTaskLumen dailyTaskLumen, User user) {
        try {
            //获取任务标准
            List<TaskLumenStandards> taskLumenStandardsList = getLumenTaskStandards(user);
            if (!CollectionUtils.isEmpty(taskLumenStandardsList)) {
                Map<String, TaskLumenStandards> taskLumenStandardsMap = taskLumenStandardsList.stream().collect(Collectors.toMap(TaskLumenStandards::getTaskId, Function.identity()));

                TaskLumenStandards taskLumenStandards = taskLumenStandardsMap.get(taskId);

                Map<String, Integer> taskLumenMap = getTaskLumenMap(taskLumen, dailyTaskLumen);

                //实际完成数量
                Integer realCount = Optional.ofNullable(taskLumenMap.get(taskId)).orElse(0);
                //目标完成数据
                Integer targetCount = Optional.ofNullable(taskLumenStandards.getTargetCount()).orElse(0);

                //如果完成既定目标，且未领取当前奖励，则给予奖励
                if (realCount >= targetCount) {
                    //赠送对应的奖励
                    if (Integer.valueOf(taskId) != 1) {
                        lumenService.receiveSystemRewardLumen(user.getLoginName(), taskLumenStandards.getReward());
                    }
                    //记录领取日志
                    addLumenRewardLog(user, taskLumenStandards.getTaskId(), taskLumenStandards.getTaskName(), taskLumenStandards.getReward());

                    com.lx.pl.enums.TaskLumenStandards standards = com.lx.pl.enums.TaskLumenStandards.getByTaskId(taskId);
                    if (standards != null && Objects.equals(standards.getTaskGroupId(), TaskGroup.ACHIEVEMENT.getId())) {
                        //更新长久任务
                        mongoTemplate.updateFirst(
                                new Query(Criteria.where("userId").is(user.getId())),
                                new Update().addToSet("rewardTaskIds", taskId),
                                TaskLumen.class
                        );
                    } else {
                        //更新每日送的lumen
                        if (Integer.valueOf(taskId) == 1) {
                            LambdaUpdateWrapper<User> luw = new LambdaUpdateWrapper();
                            luw.set(User::getDailyLumens, 10);
                            luw.set(User::getUseDailyLumens, 0);
                            luw.eq(User::getId, user.getId());
                            userMapper.update(null, luw);
                        }

                        //更新每日任务完成数据
                        mongoTemplate.updateFirst(
                                new Query(Criteria.where("userId").is(user.getId())),
                                new Update().addToSet("rewardTaskIds", taskId),
                                DailyTaskLumen.class
                        );
                    }
                } else {
                    log.info("用户：{}，任务：{}，实际完成数量：{}，目标完成数量：{}，不能领取奖励", user.getLoginName(), taskId, realCount, targetCount);
                }

            }

        } catch (Exception e) {
            log.error("用户：{}，领取任务奖励报错，报错信息为：", user.getLoginName(), e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public void addLumenRewardLog(User user, String taskId, String taskName, Integer taskReward) {
        LumenRewardLog lumenRewardLog = new LumenRewardLog();
        lumenRewardLog.setUserId(user.getId());
        lumenRewardLog.setUserLoginName(user.getLoginName());
        lumenRewardLog.setTaskId(taskId);
        lumenRewardLog.setTaskName(taskName);
        lumenRewardLog.setTaskReward(taskReward);
        lumenRewardLog.setCreateTime(LocalDateTime.now());
        lumenRewardLogRepository.insert(lumenRewardLog);
    }

    public CommPageInfo<LumenRewardLog> getTaskRewardLog(String rewardLogId, Integer pageSize, User user) throws Exception {

        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(user.getId())); // 精确匹配当前用户id

        // 游标分页：基于上一页最后一条记录的 ID
        if (StringUtil.isNotBlank(rewardLogId)) {
            query.addCriteria(Criteria.where("id").lt(new ObjectId(rewardLogId))); // 仅获取 ID 小于游标的记录
        }

        query.with(Sort.by(Sort.Direction.DESC, "id"));

        // 设置分页
        query.limit(pageSize);
        List<LumenRewardLog> lumenRewardLogList = mongoTemplate.find(query, LumenRewardLog.class);
        String lastId = CollectionUtils.isEmpty(lumenRewardLogList) ? "" : lumenRewardLogList.get(lumenRewardLogList.size() - 1).getId();

        return buildPromptPageInfo(pageSize, lumenRewardLogList, lastId);
    }

    private CommPageInfo<LumenRewardLog> buildPromptPageInfo(Integer pageSize, List<LumenRewardLog> lumenRewardLogList, String lastId) throws Exception {
        CommPageInfo<LumenRewardLog> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(lumenRewardLogList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(lastId);
        return commPageInfo;
    }

    public WatchADTaskDetailVO queryWatchADTaskDetail(User user) {
        //获取观看广告任务标准
        List<TaskLumenStandards> watchADStandardsList = null;
        try {
            List<TaskLumenStandards> taskLumenStandardsList = getLumenTaskStandards(user);
            if (!CollectionUtils.isEmpty(taskLumenStandardsList)) {
                for (TaskLumenStandards taskLumenStandards : taskLumenStandardsList) {
                    if (com.lx.pl.enums.TaskLumenStandards.WATCH_AD_TASK_IDS_SET.contains(taskLumenStandards.getTaskId())) {
                        if (watchADStandardsList == null) {
                            watchADStandardsList = new ArrayList<>();
                        }
                        watchADStandardsList.add(taskLumenStandards);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取观看广告任务标准异常, ", e);
        }
        if (CollectionUtils.isEmpty(watchADStandardsList)) {
            return null;
        }
        //获取用户每日完成信息
        DailyTaskLumen dailyTaskLumen = taskLumenService.getDailyTaskLumenByUserId(user);
        Set<String> dailyFinishTaskIds = CollectionUtils.isEmpty(dailyTaskLumen.getFinishTaskIds()) ? new HashSet<>() : dailyTaskLumen.getFinishTaskIds();

        Boolean finishTaskIdChanged = Boolean.FALSE;
        TaskLumenRsult nextWatchADTask = null;
        for (TaskLumenStandards taskLumenStandards : watchADStandardsList) {
            TaskLumenRsult taskLumenRsult = new TaskLumenRsult();
            taskLumenRsult.setTaskId(taskLumenStandards.getTaskId());
            taskLumenRsult.setTaskGroupId(taskLumenStandards.getTaskGroupId());
            taskLumenRsult.setTaskName(taskLumenStandards.getTaskName());
            taskLumenRsult.setTargetCount(taskLumenStandards.getTargetCount());
            taskLumenRsult.setReward(taskLumenStandards.getReward());
            taskLumenRsult.setDescription(taskLumenStandards.getDescription());
            taskLumenRsult.setFinishCount(dailyTaskLumen.getDailyWatchADNums());

            if (!dailyFinishTaskIds.contains(taskLumenRsult.getTaskId())) {
                //对比已经完成的数量和目标数据进行对比
                if (dailyTaskLumen.getDailyWatchADNums() >= taskLumenStandards.getTargetCount()) {
                    taskLumenRsult.setBeFinish(Boolean.TRUE);
                    finishTaskIdChanged = Boolean.TRUE;
                    dailyFinishTaskIds.add(taskLumenRsult.getTaskId());
                } else {
                    taskLumenRsult.setBeFinish(Boolean.FALSE);
                    if (nextWatchADTask == null || taskLumenRsult.getTargetCount() < nextWatchADTask.getTargetCount()) {
                        nextWatchADTask = taskLumenRsult;
                    }
                }
            }
        }

        dealNextFinishTime(nextWatchADTask, dailyTaskLumen);

        //如果有完成的任务变动，则更新对应的数据
        if (finishTaskIdChanged) {
            //更新每日任务完成数据
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("userId").is(user.getId())),
                    new Update().set("finishTaskIds", dailyFinishTaskIds),
                    DailyTaskLumen.class
            );
        }

        WatchADTaskDetailVO watchADTaskDetailVO = new WatchADTaskDetailVO(5, 5, null,
                redisService.stringGet(LogicConstants.LAST_BEGIN_WATCH_AD_TIME_KEY + user.getId()), null);
        if (nextWatchADTask != null) {
            watchADTaskDetailVO.setWatchedCount(nextWatchADTask.getFinishCount());
            watchADTaskDetailVO.setNextWatchTime(nextWatchADTask.getNextFinishTime());
            watchADTaskDetailVO.setRewardLumen(nextWatchADTask.getReward());
        }

        return watchADTaskDetailVO;
    }

    public Boolean finishWatchAD(User user) {
        boolean result = false;
        RLock lock = redissonClient.getLock(LockPrefixConstant.FINISH_WATCH_AD_TASK_LOCK_PREFIX + user.getLoginName());
        try {
            if (!lock.tryLock()) {
                log.warn("用户【{}】完成观看广告任务被拦截", user.getLoginName());
                return result;
            }

            long nowTime = Instant.now().getEpochSecond();
            DailyTaskLumen dailyTaskLumen = taskLumenService.getDailyTaskLumenByUserId(user);
            if (dailyTaskLumen.getLastWatchADTime() != null && nowTime < dailyTaskLumen.getLastWatchADTime() + LogicConstants.WATCH_AD_INTERVAL_TIME) {
                log.warn("用户【{}】完成观看广告任务失败, 未到下次观看广告时间, 上次观看广告时间为【{}】", user.getLoginName(), dailyTaskLumen.getLastWatchADTime());
                return result;
            }
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("userId").is(user.getId())),
                    new Update().inc("dailyWatchADNums", 1)
                            .set("lastWatchADTime", nowTime),
                    DailyTaskLumen.class
            );
            //领取奖励
            result = receiveTaskReward(new ArrayList<>(com.lx.pl.enums.TaskLumenStandards.WATCH_AD_TASK_IDS_SET), user);
        } catch (Exception e) {
            log.error("用户【{}】完成观看广告任务异常", user.getLoginName(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return result;
    }

    public Boolean beginWatchAD(User user) {
        boolean result = false;
        RLock lock = redissonClient.getLock(LockPrefixConstant.BEGIN_WATCH_AD_TASK_LOCK_PREFIX + user.getLoginName());
        try {
            if (!lock.tryLock()) {
                log.warn("用户【{}】拉起广告被拦截", user.getLoginName());
                return result;
            }
            long nowTime = Instant.now().getEpochSecond();
            DailyTaskLumen dailyTaskLumen = taskLumenService.getDailyTaskLumenByUserId(user);
            if (dailyTaskLumen.getLastWatchADTime() != null && nowTime < dailyTaskLumen.getLastWatchADTime() + LogicConstants.WATCH_AD_INTERVAL_TIME) {
                log.warn("用户【{}】拉起广告失败, 未到下次观看广告时间, 上次观看广告时间为【{}】", user.getLoginName(), dailyTaskLumen.getLastWatchADTime());
                return result;
            }
            //记录拉起广告时间
            redisService.stringSet(LogicConstants.LAST_BEGIN_WATCH_AD_TIME_KEY + user.getId(), String.valueOf(nowTime), 2, TimeUnit.MINUTES);
            result = true;
        } catch (Exception e) {
            log.error("用户【{}】拉起广告异常", user.getLoginName(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return result;
    }
}
