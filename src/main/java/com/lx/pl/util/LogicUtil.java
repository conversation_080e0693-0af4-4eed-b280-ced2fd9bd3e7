package com.lx.pl.util;

import com.lx.pl.dto.alarm.AlarmDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
@Slf4j
public class LogicUtil {

    /**
     * 根据像素计算点数
     */
    public static int calculateCostLumenByPixel(int picWidth, int picHeight) {
        //计算总像素在200W以下，200W-300W，300W-400W，400W以上的数量
        if (picWidth * picHeight <= 2000000) {
            return 1;
        } else if (picWidth * picHeight <= 3000000) {
            return 2;
        } else if (picWidth * picHeight <= 4000000) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * 构建告警信息
     */
    public static AlarmDTO buildAlarmMessage(String type, String source, String detail, String userInfo, Exception e) {
        AlarmDTO alarmDTO = null;
        try {
            alarmDTO = AlarmDTO.builder()
                    .type(type)
                    .source(source)
                    .detail(detail)
                    .userInfo(userInfo)
                    .build();
            if (e != null) {
                //异常告警
                alarmDTO.setExceptionClassName(e.getClass().getName());
                StackTraceElement[] stackTraceElements = e.getStackTrace();
                if (stackTraceElements != null && stackTraceElements.length > 0) {
                    alarmDTO.setExceptionLineNum(stackTraceElements[0].getLineNumber());
                }
            }
        } catch (Exception e1) {
            log.error("构建告警信息异常, ", e1);
        }
        return alarmDTO;
    }
}
