package com.lx.pl.manager;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DigestUtils;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.stereotype.Component;

/**
 * 通过Redis存储和验证token的实现类
 */
@Component
public class RedisTokenManager implements TokenManager {

    private static final long TOKEN_EXPIRES_DAYS = 7;

    private RedisTemplate<String, String> redis;

    @Qualifier("redisTemplate")
    @Autowired
    public void setRedis(RedisTemplate redis) {
        this.redis = redis;
        //泛型设置成Long后必须更改对应的序列化方案
        redis.setKeySerializer(new JdkSerializationRedisSerializer());
    }

    @Override
    public String createToken(long userId, long userRole) {
        //使用uuid作为源token
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String token = DigestUtils.sha1DigestAsHex(uuid + userId + "e23ktjsdf");
        //存储到redis并设置过期时间
        redis.boundValueOps(token).set(String.valueOf(userId), TOKEN_EXPIRES_DAYS, TimeUnit.DAYS);
        redis.boundValueOps(String.valueOf(userId)).set(token, TOKEN_EXPIRES_DAYS, TimeUnit.DAYS);
        return token;
    }


    public boolean checkToken(String token) {
        if (token == null) {
            return false;
        }
        String userId = redis.boundValueOps(token).get();
        if (userId == null) {
            return false;
        }
        //如果验证成功，说明此用户进行了一次有效操作，延长token的过期时间
        redis.boundValueOps(String.valueOf(userId)).expire(TOKEN_EXPIRES_DAYS, TimeUnit.DAYS);
        redis.boundValueOps(token).expire(TOKEN_EXPIRES_DAYS, TimeUnit.DAYS);
        return true;
    }


    @Override
    public long getUserId(String token) {
        if (token == null) {
            return -1;
        }
        String userId = redis.boundValueOps(token).get();
        return Long.parseLong(userId);
    }

    public void deleteToken(String token, String userId) {
        redis.delete(token);
        redis.delete(userId);
    }

    @Override
    public String getTokenByUserId(long userId) {
        return redis.boundValueOps(String.valueOf(userId)).get();
    }

    @Override
    public String getUserIdByToken(String token) {
        return redis.boundValueOps(token).get();
    }

    @Override
    public Long getKeyExpireTime(String key) {
        // 使用getExpire方法获取键的过期时间
        return redis.getExpire(key);
    }

    @Override
    public void createUserIdByToken(String token, Long userId, Long expireTime, TimeUnit timeUnit) {
        redis.boundValueOps(token).set(String.valueOf(userId), expireTime, timeUnit);
    }

}
