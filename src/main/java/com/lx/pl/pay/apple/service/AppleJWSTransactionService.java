package com.lx.pl.pay.apple.service;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.domain.AppleJWSTransaction;

import java.util.List;

public interface AppleJWSTransactionService extends IService<AppleJWSTransaction> {
    boolean existsByTransactionId(String transactionId);

    AppleJWSTransaction getLastProcessedTransactionTime(String originalTransactionId);

    void saveTransaction(JWSTransactionDecodedPayload payload, User userId);

    List<AppleJWSTransaction> querySubscriptionListByOriginalTransactionId(String originalTransactionIde);

    AppleJWSTransaction queryByTransactionId(String transactionId);
}