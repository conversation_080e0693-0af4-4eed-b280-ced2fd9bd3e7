package com.lx.pl.pay.apple.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.apple.itunes.storekit.model.JWSRenewalInfoDecodedPayload;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.apple.domain.AppleJWSRenewalInfo;
import com.lx.pl.pay.apple.mapper.AppleJWSRenewalInfoMapper;
import com.lx.pl.pay.apple.service.AppleJWSRenewalInfoService;
import com.lx.pl.pay.apple.service.PayAppleUserRelationService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Service
public class AppleJWSRenewalInfoServiceImpl extends ServiceImpl<AppleJWSRenewalInfoMapper, AppleJWSRenewalInfo> implements AppleJWSRenewalInfoService {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Resource
    private PayAppleUserRelationService payAppleUserRelationService;

    @Override
    public void saveOrUpdateIfNeed(JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload, String notificationUUID) {
        if (this.existsByNotificationUUID(notificationUUID)) {
            log.info("该通知已经存在，无需保存");
        }
        saveNewRenewalInfo(jwsRenewalInfoDecodedPayload, notificationUUID);
    }

    private void saveNewRenewalInfo(JWSRenewalInfoDecodedPayload payload, String notificationUUID) {
        if (payload == null) {
            log.info("JWSRenewalInfoDecodedPayload 为空，无法保存");
            return;
        }

        AppleJWSRenewalInfo renewalInfo = new AppleJWSRenewalInfo();

        // 设置基本信息
        renewalInfo.setOriginalTransactionId(payload.getOriginalTransactionId());
        renewalInfo.setAutoRenewProductId(payload.getAutoRenewProductId());
        renewalInfo.setProductId(payload.getProductId());

        // 设置自动续订状态
        if (payload.getAutoRenewStatus() != null) {
            renewalInfo.setAutoRenewStatus(payload.getAutoRenewStatus().getValue());
        }

        renewalInfo.setIsInBillingRetryPeriod(payload.getIsInBillingRetryPeriod());

        // 设置优惠相关信息
        if (payload.getOfferDiscountType() != null) {
            renewalInfo.setOfferDiscountType(payload.getOfferDiscountType().getValue());
        }

        if (payload.getOfferType() != null) {
            renewalInfo.setOfferType(payload.getOfferType().getValue());
        }

        // 设置优惠ID列表
        if (CollUtil.isNotEmpty(payload.getEligibleWinBackOfferIds())) {
            renewalInfo.setEligibleWinBackOfferIds(String.join(",", payload.getEligibleWinBackOfferIds()));
        }

        // 设置基本字段
        renewalInfo.setOfferIdentifier(payload.getOfferIdentifier());
        renewalInfo.setCurrency(payload.getCurrency());
        renewalInfo.setRenewalPrice(payload.getRenewalPrice());

        // 设置时间相关字段，添加空值检查
        if (payload.getRenewalDate() != null) {
            renewalInfo.setRenewalDate(payload.getRenewalDate() / 1000);
        }

        if (payload.getSignedDate() != null) {
            renewalInfo.setSignedDate(payload.getSignedDate() / 1000);
        }

        if (payload.getRecentSubscriptionStartDate() != null) {
            renewalInfo.setRecentSubscriptionStartDate(payload.getRecentSubscriptionStartDate() / 1000);
        }

        if (payload.getGracePeriodExpiresDate() != null) {
            renewalInfo.setGracePeriodExpiresDate(payload.getGracePeriodExpiresDate() / 1000);
        }

        // 设置状态相关字段
        if (payload.getExpirationIntent() != null) {
            renewalInfo.setExpirationIntent(payload.getExpirationIntent().getValue());
        }

        if (payload.getPriceIncreaseStatus() != null) {
            renewalInfo.setPriceIncreaseStatus(payload.getPriceIncreaseStatus().getValue());
        }

        // 设置环境信息
        if (payload.getEnvironment() != null) {
            renewalInfo.setEnvironment(payload.getEnvironment().getValue());
        }

        // 设置通知ID和创建时间
        renewalInfo.setNotificationUUID(notificationUUID);
        renewalInfo.setCreateTime(LocalDateTime.now());

        try {
            this.save(renewalInfo);
            log.info("续订信息保存成功: originalTransactionId={}", renewalInfo.getOriginalTransactionId());
        } catch (Exception e) {
            log.error("保存续订信息失败: originalTransactionId={}", renewalInfo.getOriginalTransactionId(), e);
            throw new RuntimeException("Failed to save renewal info", e);
        }
    }

    private boolean existsByNotificationUUID(String notificationUUID) {
        return this.lambdaQuery().eq(AppleJWSRenewalInfo::getNotificationUUID, notificationUUID)
                .exists();
    }


    // 基础的 CRUD 操作由 ServiceImpl 提供
}