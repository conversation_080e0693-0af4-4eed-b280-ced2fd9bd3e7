package com.lx.pl.pay.apple.service.impl;

import cn.hutool.core.util.StrUtil;
import com.apple.itunes.storekit.client.AppStoreServerAPIClient;
import com.apple.itunes.storekit.client.GetTransactionHistoryVersion;
import com.apple.itunes.storekit.model.*;
import com.apple.itunes.storekit.verification.SignedDataVerifier;
import com.apple.itunes.storekit.verification.VerificationException;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.AppleErrorCode;
import com.lx.pl.exception.PayAppleException;
import com.lx.pl.pay.apple.service.*;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.apple.domain.PayAppleUserRelation;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.util.PayLogContextHolder;

import com.lx.pl.service.CommMessageService;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import groovy.lang.Lazy;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.apple.itunes.storekit.model.TransactionHistoryRequest.ProductType.AUTO_RENEWABLE;
import static com.apple.itunes.storekit.model.TransactionHistoryRequest.ProductType.CONSUMABLE;
import static com.lx.pl.constant.LockPrefixConstant.TRANSACTION_ID_LOCK_PREFIX;
import static com.lx.pl.pay.apple.service.impl.ApplePayServiceImpl.buildAppleSubscriptionCurrent;

@Service
public class ApplePayCallbackServiceImpl implements ApplePayCallbackService, ApplicationContextAware {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");
    @Autowired
    @Lazy
    private ApplePayService applePayService;
    @Autowired
    private PayAppleProductService payAppleProductService;
    @Resource
    private PayAppleUserRelationService payAppleUserRelationService;
    @Resource
    private AppleJWSTransactionService appleTransactionService;
    @Resource
    private AppleJWSRenewalInfoService appleRenewalInfoService;
    @Resource
    public SignedDataVerifier signedPayloadVerifier;
    @Resource
    private UserService userService;
    @Resource
    PayApplePurchaseRecordService payApplePurchaseRecordService;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;
    @Resource
    private RedissonClient redissonClient;
    private ApplicationContext applicationContext;
    @Resource
    private VipService vipService;
    @Resource
    private PayAppleUpgradeLogService payAppleUpgradeLogService;
    @Autowired
    private MongoTemplate mongoTemplate;


    public static void main(String[] args) {
        Environment environment = Environment.SANDBOX.getValue()
                .equalsIgnoreCase("Sandbox") ? Environment.SANDBOX : Environment.PRODUCTION;
        Set<InputStream> rootCas = Set.of(Objects.requireNonNull(ApplePayCallbackServiceImpl.class.getClassLoader()
                .getResourceAsStream("apple/AppleRootCA-G2.cer")), Objects.requireNonNull(ApplePayCallbackServiceImpl.class.getClassLoader()
                .getResourceAsStream("apple/AppleRootCA-G3.cer")));
        // appAppleId must be provided for the Production environment
        SignedDataVerifier signedDataVerifier = new SignedDataVerifier(rootCas, "com.piclumen", 6720725066L, environment, true);


        String encodedKey = null;
        Path filePath = null;
        try {
            filePath = Paths.get(ApplePayServiceImpl.class.getClassLoader()
                    .getResource("apple/SubscriptionKey_D477NXY4AC.p8").toURI());
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        try {
            encodedKey = Files.readString(filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        AppStoreServerAPIClient appStoreServerAPIClient = new AppStoreServerAPIClient(encodedKey, "D477NXY4AC", "69a6de80-ea80-47e3-e053-5b8c7c11a4d1", "com.piclumen", environment);

        try {
            JWSTransactionDecodedPayload jwsTransactionDecodedPayload = signedDataVerifier.verifyAndDecodeTransaction("***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
            List<String> transactions = new LinkedList<>();
            HistoryResponse response = null;
            TransactionHistoryRequest request = new TransactionHistoryRequest().sort(TransactionHistoryRequest.Order.ASCENDING)
                    .revoked(false).productTypes(List.of(CONSUMABLE));
            do {
                try {
                    response = appStoreServerAPIClient.getTransactionHistory(jwsTransactionDecodedPayload.getOriginalTransactionId(), response != null ? response.getRevision() : null, request, GetTransactionHistoryVersion.V2);
                    for (String signedTransactionInfo : response.getSignedTransactions()) {
                        JWSTransactionDecodedPayload jwsTransactionDecodedPayload1 = signedDataVerifier.verifyAndDecodeTransaction(signedTransactionInfo);
                        System.out.println("transactions: " + jwsTransactionDecodedPayload1.getTransactionId() + "  " + jwsTransactionDecodedPayload1.getIsUpgraded() + "  " + jwsTransactionDecodedPayload1);
                    }
                    transactions.addAll(response.getSignedTransactions());
                } catch (Exception e) {
                    throw new PayAppleException(AppleErrorCode.GET_HISTORY_ERROR, e);
                }
            } while (response.getHasMore());
        } catch (Exception e) {
//            log.error("Failed to verify test notification: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 处理 Apple 的订阅回调事件
     *
     * @param decodePayload 包含 Apple 发送的通知内容
     */
    @Override
    public void processSubscriptionEvent(String decodePayload) {
        ResponseBodyV2DecodedPayload responseBodyV2DecodedPayload = null;
        try {
            responseBodyV2DecodedPayload = signedPayloadVerifier.verifyAndDecodeNotification(decodePayload);
            log.info("responseBodyV2DecodedPayload1: {}", responseBodyV2DecodedPayload);
        } catch (Exception e) {
            throw new RuntimeException("Failed to process subscription event: " + e.getMessage(), e);
        }

        Data data = responseBodyV2DecodedPayload.getData();
        if (data == null) {
            return;
        }
        log.info("data: {}", data);

        String signedRenewalInfo = data.getSignedRenewalInfo();
        JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload = null;
        if (signedRenewalInfo != null) {
            try {
                jwsRenewalInfoDecodedPayload = signedPayloadVerifier.verifyAndDecodeRenewalInfo(signedRenewalInfo);
                log.info("jwsRenewalInfoDecodedPayload: {}", jwsRenewalInfoDecodedPayload);
            } catch (VerificationException e) {
                log.error("Failed to verify renewal info: {}", e.getMessage(), e);
            }
        }
        String signedTransactionInfo = data.getSignedTransactionInfo();
        JWSTransactionDecodedPayload transaction = null;
        try {
            transaction = signedPayloadVerifier.verifyAndDecodeTransaction(signedTransactionInfo);
            log.info("transaction: {}", transaction);
        } catch (VerificationException e) {
            log.error("Failed to verify transaction info: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }

        // 根据通知类型处理
        String lockKey = TRANSACTION_ID_LOCK_PREFIX + transaction.getOriginalTransactionId();
        RLock lock = redissonClient.getLock(lockKey);
        String logingName = null;
        try {
            lock.lock();
            log.info("handleNotification处理开始: {}", transaction.getTransactionId());
            logingName = applicationContext.getBean(ApplePayCallbackServiceImpl.class)
                    .handleNotification(responseBodyV2DecodedPayload, jwsRenewalInfoDecodedPayload, transaction);
            log.info("handleNotification处理完成: {}", transaction.getTransactionId());
            vipService.resettingPersonalLumens(logingName);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
            try {
                if (StrUtil.isNotBlank(logingName)) {
                    User user = userService.getByLoginName(logingName);
                    if (user != null) {
                        updateUserVipStatus(user.getId());
                        saveToMongoLog(user, transaction, jwsRenewalInfoDecodedPayload);
                    }
                }
            } catch (Exception e) {
                log.error("更新用户VIP等级2 失败: {}", e.getMessage(), e);
            }
        }
    }

    private void saveToMongoLog(User user, JWSTransactionDecodedPayload payload, JWSRenewalInfoDecodedPayload renewalInfo) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("uuid", PayLogContextHolder.getLogUUID());
            logData.put("transaction", payload);
            logData.put("renewalInfo", renewalInfo);
            logData.put("type", "WEBHOOK");
            logData.put("createTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            logData.put("userId", user.getId());
            logData.put("loginName", user.getLoginName());

            // 创建更清晰的数据结构
            Map<String, Object> transactionInfo = new HashMap<>();
            transactionInfo.put("transactionId", payload.getTransactionId());
            transactionInfo.put("originalTransactionId", payload.getOriginalTransactionId());
            transactionInfo.put("productId", payload.getProductId());
            transactionInfo.put("purchaseDate", payload.getPurchaseDate());
            logData.put("transactionInfo", transactionInfo);
            // 指定集合名称并处理异常
            mongoTemplate.save(logData, "apple_pay_logs");
        } catch (Exception e) {
            log.error("saveToMongoLog error {} {}", 1, 3, e);
        }
    }


    private void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        log.info("更新用户VIP等级1 start: loginName={} newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        userService.updateUserVipInfo(subscription, userId);
        log.info("更新用户VIP等级1 end: loginName={}, newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }

    @Transactional(rollbackFor = Exception.class)
    public String handleNotification(ResponseBodyV2DecodedPayload responseBodyV2DecodedPayload, JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload, JWSTransactionDecodedPayload transaction) {
        String notificationUUID = responseBodyV2DecodedPayload.getNotificationUUID();

        Subtype subtype = responseBodyV2DecodedPayload.getSubtype();
        appleRenewalInfoService.saveOrUpdateIfNeed(jwsRenewalInfoDecodedPayload, notificationUUID);


        switch (responseBodyV2DecodedPayload.getNotificationType()) {
            case SUBSCRIBED:
                // 判断是否处理过
                if (appleTransactionService.existsByTransactionId(transaction.getTransactionId())) {
                    log.info("已处理过该通知, transactionId={}", transaction.getTransactionId());
                    PayAppleUserRelation userRelation = payAppleUserRelationService.queryByOriginalTransactionId(transaction.getOriginalTransactionId());
                    return userRelation != null ? userRelation.getLoginName() : null;
                }
                handleSubscribed(subtype, transaction);
                break;
            case DID_RENEW:
                // 判断是否处理过
                if (appleTransactionService.existsByTransactionId(transaction.getTransactionId())) {
                    log.info("已处理过该通知, transactionId={}", transaction.getTransactionId());
                    PayAppleUserRelation userRelation = payAppleUserRelationService.queryByOriginalTransactionId(transaction.getOriginalTransactionId());
                    return userRelation != null ? userRelation.getLoginName() : null;
                }
                // 自动续订成功通知
                // Apple 已经成功续订用户的订阅。通常更新订阅到期时间和状态。
                handleDidRenew(transaction);
                break;
            case DID_CHANGE_RENEWAL_PREF:
                // 判断是否处理过
                if (appleTransactionService.existsByTransactionId(transaction.getTransactionId())) {
                    log.info("已处理过该通知, transactionId={}", transaction.getTransactionId());
                    PayAppleUserRelation userRelation = payAppleUserRelationService.queryByOriginalTransactionId(transaction.getOriginalTransactionId());
                    return userRelation != null ? userRelation.getLoginName() : null;
                }
                // 客户对其订阅计划进行了更改 升级 降级 取消降级
                handleRenewalChange(subtype, jwsRenewalInfoDecodedPayload, transaction);
                break;
            case DID_CHANGE_RENEWAL_STATUS:
                // 客户对订阅续订状态进行了更改
                handleRenewalStatusChange(subtype, jwsRenewalInfoDecodedPayload, transaction);
                break;
            case EXPIRED:
                // 订阅到期通知
                handleExpired(subtype, jwsRenewalInfoDecodedPayload, transaction);
                break;
            case DID_FAIL_TO_RENEW:
                // 订阅续订失败，进入计费重试期。
                //  订阅续订失败并进入启用计费宽限期的计费重试期。GRACE_PERIOD
                log.info("订阅续订失败，进入计费重试期 或者 订阅续订失败并进入启用计费宽限期的计费重试期: {}", subtype);
                break;
            case GRACE_PERIOD_EXPIRED:
                // 订阅退出计费宽限期（并继续计费重试）。。
                log.info("订阅退出计费宽限期（并继续计费重试）: {}", subtype);
                break;
            case REFUND:
                log.info("订阅退款通知: {}", subtype);
                // 添加处理退费逻辑
//                handleRefund(transaction);
//                break;
            case CONSUMPTION_REQUEST:

            default:
                log.info("未处理的通知类型: {} {}", responseBodyV2DecodedPayload.getNotificationType(), responseBodyV2DecodedPayload.getSubtype());
                handleDefault(transaction);
        }
        PayAppleUserRelation userRelation = payAppleUserRelationService.queryByOriginalTransactionId(transaction.getOriginalTransactionId());
        return userRelation != null ? userRelation.getLoginName() : null;
    }

    private void handleDefault(JWSTransactionDecodedPayload transaction) {
        PayAppleUserRelation userRelation = payAppleUserRelationService.lambdaQuery()
                .eq(PayAppleUserRelation::getOriginalTransactionId, transaction.getOriginalTransactionId()).one();
        if (appleTransactionService.existsByTransactionId(transaction.getTransactionId())) {
            log.info("handleDefault 已处理过该通知, transactionId={} transaction: {}", transaction.getTransactionId(), transaction);
            return;
        }
        if (userRelation != null) {
            User userById = userService.getUserById(userRelation.getUserId());
            appleTransactionService.saveTransaction(transaction, userById);
        } else {
            appleTransactionService.saveTransaction(transaction, null);
        }
        log.info("handleDefault 未处理的通知类型 end: {} {}", transaction.getOriginalPurchaseDate(), transaction.getTransactionId());
    }

    private void handleRefund(JWSTransactionDecodedPayload transaction) {

    }

    private void handleExpired(Subtype subtype, JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload, JWSTransactionDecodedPayload transaction) {

        PayAppleUserRelation userRelation = payAppleUserRelationService.queryRelationByOriginalTransactionId(transaction, () -> {
            return applePayService.getTransactionHistory(transaction, AUTO_RENEWABLE);
        });
        // 2. 获取当前订阅信息
        SubscriptionCurrent currentSubscription = subscriptionCurrentService.lambdaQuery()
                .eq(SubscriptionCurrent::getUserId, userRelation.getUserId())
                .eq(SubscriptionCurrent::getSubscriptionId, transaction.getOriginalTransactionId())
                .eq(SubscriptionCurrent::getInvalid, false)
                .one();

        if (currentSubscription == null) {
            log.error("未找到当前订阅信息: userId={}", userRelation.getUserId());
            throw new RuntimeException("未找到当前订阅信息");
        }
        User user = userService.getUserById(userRelation.getUserId());
        appleTransactionService.saveTransaction(transaction, user);

        currentSubscription.setAutoRenewStatus(0);
        subscriptionCurrentService.lambdaUpdate().eq(SubscriptionCurrent::getId, currentSubscription.getId())
                .eq(SubscriptionCurrent::getInvalid, false)
                .set(SubscriptionCurrent::getAutoRenewStatus, currentSubscription.getAutoRenewStatus())
                .update();
        switch (subtype) {
            case VOLUNTARY:
                // 由于客户选择取消，订阅已过期
                log.info("由于客户选择取消，订阅已过期: {}", subtype);
                break;
            case PRODUCT_NOT_FOR_SALE:
                //由于开发者下架订阅，导致订阅过期，并且续订失败。
                log.info("由于开发者下架订阅，导致订阅过期，并且续订失败: {}", subtype);
                break;
            case BILLING_RETRY:
                // 由于计费重试期结束且未恢复订阅，因此订阅已过期。
                log.info("由于计费重试期结束且未恢复订阅，因此订阅已过期: {}", subtype);
                break;
            case PRICE_INCREASE:
                // 自动续订订阅已到期，因为客户不同意需要征得同意的价格上涨。
                log.info("由于价格上涨，订阅已到期: {}", subtype);
                break;
            default:
                log.info("handleExpired subtype: {}", subtype);
        }
    }

    private void handleRenewalStatusChange(Subtype subtype, JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload, JWSTransactionDecodedPayload transaction) {
        String originalTransactionId = transaction.getOriginalTransactionId();

        log.info("开始处理续订状态变更事件");

        try {
            PayAppleUserRelation userRelation = payAppleUserRelationService.queryRelationByOriginalTransactionId(transaction, () -> {
                return applePayService.getTransactionHistory(transaction, AUTO_RENEWABLE);
            });
            if (!appleTransactionService.existsByTransactionId(transaction.getTransactionId())) {
                User userById = userService.getUserById(userRelation.getUserId());
                appleTransactionService.saveTransaction(transaction, userById);
            }
            SubscriptionCurrent subscriptionCurrent = subscriptionCurrentService.lambdaQuery()
                    .eq(SubscriptionCurrent::getUserId, userRelation.getUserId())
                    .eq(SubscriptionCurrent::getSubscriptionId, originalTransactionId)
                    .one();

            if (subscriptionCurrent == null) {
                throw new RuntimeException("未找到用户订阅关系");
            }

            log.info("变更前自动续订状态: {}", subscriptionCurrent.getAutoRenewStatus());
            switch (subtype) {
                case AUTO_RENEW_ENABLED:
                    subscriptionCurrent.setAutoRenewStatus(1);
                    log.info("用户启用自动续订 {} {}", subscriptionCurrent.getLoginName(), subscriptionCurrent.getSubscriptionId());
                    break;
                case AUTO_RENEW_DISABLED:
                    subscriptionCurrent.setAutoRenewStatus(0);
                    log.info("用户禁用自动续订 {} {}", subscriptionCurrent.getLoginName(), subscriptionCurrent.getSubscriptionId());
                    break;
                default:
                    log.info("其他续订状态变更: {}", subtype);
                    break;
            }

            log.info("变更后自动续订状态:  {} {}", subscriptionCurrent.getLoginName(), subscriptionCurrent.getSubscriptionId());
            subscriptionCurrentService.lambdaUpdate().eq(SubscriptionCurrent::getId, subscriptionCurrent.getId())
                    .set(SubscriptionCurrent::getAutoRenewStatus, subscriptionCurrent.getAutoRenewStatus())
                    .eq(SubscriptionCurrent::getInvalid, false)
                    .update();
            log.info("续订状态变更处理完成");
        } catch (Exception e) {
            log.error("续订状态变更处理异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    private void handleRenewalChange(Subtype subtype, JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload, JWSTransactionDecodedPayload transaction) {
        String originalTransactionId = transaction.getOriginalTransactionId();

        log.info("开始处理订阅计划变更事件: subtype={}, productId={}, autoRenewProductId={}", subtype, transaction.getProductId(), jwsRenewalInfoDecodedPayload.getAutoRenewProductId());
        String productId = transaction.getProductId();
        PayAppleProduct product = payAppleProductService.queryByProductId(productId);
        if (product == null) {
            log.error("产品信息不存在, productId: {}, transactionId: {}", productId, transaction.getTransactionId());
            throw new RuntimeException("产品信息不存在");
        }

        try {
            // 1. 获取用户订阅关系
            PayAppleUserRelation userRelation = payAppleUserRelationService.queryRelationByOriginalTransactionId(transaction, () -> {
                return applePayService.getTransactionHistory(transaction, AUTO_RENEWABLE);
            });
            // 2. 获取当前订阅信息
            SubscriptionCurrent currentSubscription = subscriptionCurrentService.lambdaQuery()
                    .eq(SubscriptionCurrent::getUserId, userRelation.getUserId())
                    .eq(SubscriptionCurrent::getOriginalTransactionId, originalTransactionId)
                    .eq(SubscriptionCurrent::getInvalid, false)
                    .one();

            if (currentSubscription == null) {
                log.error("未找到当前订阅信息: userId={}", userRelation.getUserId());
                throw new RuntimeException("未找到当前订阅信息");
            }

            // 3. 处理不同类型的变更
            if (subtype == null) {
                // 取消降级 不需要做操作，因爲降级是下个周期生效
                // If the is empty, the user changed their renewal preference back to the current subscription, effectively canceling a downgrade.subtype
                handleCancelDowngrade(userRelation, jwsRenewalInfoDecodedPayload, transaction);
//                updateSubscriptionPlan(currentSubscription, jwsRenewalInfoDecodedPayload.getAutoRenewProductId());
            } else {

                switch (subtype) {
                    case DOWNGRADE:
                        log.info("开始处理降级事件: transactionId={} productId={}, autoRenewProductId={}", transaction.getTransactionId(), productId, jwsRenewalInfoDecodedPayload.getAutoRenewProductId());
//                        updateSubscriptionPlan(currentSubscription, newProduct.getAppleProductId());
                        handleDowngrade(userRelation, jwsRenewalInfoDecodedPayload, transaction);
                        break;
                    case UPGRADE:
                        // 立即升级
                        handleUpgrade(userRelation, transaction, currentSubscription);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("订阅计划变更处理异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    private void updateUserVipLevel(Long userId, PayAppleProduct newProduct) {
        try {
            User user = userService.getUserById(userId);
            if (user != null) {
                SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
                userService.updateUserVipInfo(subscription, userId);

                log.info("更新用户VIP等级: userId={}, newPlanLevel={}", userId, newProduct.getPlanLevel());
            }
        } catch (Exception e) {
            log.error("更新用户VIP等级失败: userId={}", userId, e);
        }
    }

    private void handleCancelDowngrade(PayAppleUserRelation userRelation, JWSRenewalInfoDecodedPayload jwsRenewalInfoDecodedPayload, JWSTransactionDecodedPayload transaction) {
        log.info("处理取消降级 transactionId={} ", transaction.getTransactionId());
//        userRelation.setAutoRenewProductId(userRelation.getProductId());
        User userById = userService.getUserById(userRelation.getUserId());
        appleTransactionService.saveTransaction(transaction, userById);
    }

    private void handleDowngrade(PayAppleUserRelation userRelation, JWSRenewalInfoDecodedPayload renewalInfo, JWSTransactionDecodedPayload transaction) {
        log.info("处理降级订阅, 新产品ID: transactionId={} {}", transaction.getTransactionId(), renewalInfo.getAutoRenewProductId());
        User userById = userService.getUserById(userRelation.getUserId());
        appleTransactionService.saveTransaction(transaction, userById);
//        userRelation.setAutoRenewProductId(renewalInfo.getAutoRenewProductId());
    }

    private void handleUpgrade(PayAppleUserRelation userRelation, JWSTransactionDecodedPayload transaction, SubscriptionCurrent currentSubscription) {
        log.info("start处理升级订阅, 新产品ID: {}", transaction.getProductId());
        User user = userService.getUserById(userRelation.getUserId());
        appleTransactionService.saveTransaction(transaction, user);
        // 保存购买记录
        boolean successPurchase = payApplePurchaseRecordService.saveAppleLogicPurchaseRecord(transaction, user, Type.AUTO_RENEWABLE_SUBSCRIPTION);
        if (successPurchase) {
            PayAppleProduct newProduct = payAppleProductService.queryByProductId(transaction.getProductId());
            if (newProduct == null) {
                log.error("新产品不存在: productId={}", transaction.getProductId());
                throw new RuntimeException("新产品不存在");
            }
            String lastTransactionId = currentSubscription.getTransactionId();
            subscriptionCurrentService.updateSubscriptionPlan(currentSubscription, newProduct.getAppleProductId(), transaction);
            String oldProductId = payApplePurchaseRecordService.recallLumenByTransactionId(lastTransactionId);
            SubscriptionCurrent subscriptionCurrent = buildAppleSubscriptionCurrent(transaction, user, newProduct);
            subscriptionCurrentService.saveOrUpdateAppleSubscriptionCurrent(subscriptionCurrent);
            // 更新用户VIP状态
            SubscriptionCurrent validSubscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
            userService.updateUserVipInfo(validSubscription, user.getId());
            // 记录产品升级日志
            payAppleUpgradeLogService.logUpgrade(user.getId(), user.getLoginName(), transaction.getProductId(), oldProductId, transaction.getOriginalTransactionId(), transaction.getTransactionId());

            log.info("end处理升级订阅1, 新产品ID: {}", transaction.getProductId());
        }
        log.info("end处理升级订阅2, 新产品ID: {}", transaction.getProductId());
    }

    private void handleDidRenew(JWSTransactionDecodedPayload transaction) {
        String originalTransactionId = transaction.getOriginalTransactionId();

        log.info("开始处理自动续订事件");
        String productId = transaction.getProductId();
        PayAppleProduct product = payAppleProductService.queryByProductId(productId);
        if (product == null) {
            log.error("产品信息不存在, productId: {}, transactionId: {}", productId, transaction.getTransactionId());
            throw new RuntimeException("产品信息不存在");
        }
        try {
            PayAppleUserRelation userRelation = payAppleUserRelationService.queryRelationByOriginalTransactionId(transaction, () -> {
                return applePayService.getTransactionHistory(transaction, AUTO_RENEWABLE);
            });

            // 2. 处理交易记录
            processResubscribeTransaction(userRelation, transaction, product);
            log.info("自动续订处理完成");
        } catch (Exception e) {
            log.error("自动续订处理异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    private void handleSubscribed(Subtype subtype, JWSTransactionDecodedPayload transaction) {
        log.info("开始处理订阅事件 transactionId={}", transaction.getTransactionId());
        try {
            String productId = transaction.getProductId();
            PayAppleProduct product = payAppleProductService.queryByProductId(productId);
            if (product == null) {
                log.error("产品信息不存在, productId: {}, transactionId: {}", productId, transaction.getTransactionId());
                throw new RuntimeException("产品信息不存在");
            }
            switch (subtype) {
                case INITIAL_BUY:
                    handleInitialBuy(transaction, product);
                    break;
                case RESUBSCRIBE:
                    handleResubscribe(transaction, product);
                    break;
                default:
                    log.warn("未知的订阅子类型: {}", subtype);
            }
            log.info("订阅事件处理完成");
            // 处理jwsRenewalInfoDecodedPayload

        } catch (Exception e) {
            log.error("处理订阅事件失败: {}", e.getMessage(), e);
            throw e;
        }


    }

    private void handleInitialBuy(JWSTransactionDecodedPayload transaction, PayAppleProduct product) {
        String appAccountToken = transaction.getAppAccountToken().toString();
        log.info("处理首次购买事件开始");
        PayAppleUserRelation userRelation = payAppleUserRelationService.lambdaQuery()
                .eq(PayAppleUserRelation::getOriginalTransactionId, transaction.getOriginalTransactionId()).one();
        if (userRelation == null) {
            log.info("更新新的用户订阅关系");
            userRelation = payAppleUserRelationService.lambdaQuery()
                    .eq(PayAppleUserRelation::getAppAccountToken, appAccountToken).one();
            if (userRelation == null) {
                log.error("用户订阅关系不存在, appAccountToken: {}", appAccountToken);
                throw new RuntimeException("用户订阅关系不存在");
            }
            log.info("新用户订阅关系创建成功");
        }

        if (StrUtil.isBlank(userRelation.getOriginalTransactionId())) {
            userRelation.setOriginalTransactionId(transaction.getOriginalTransactionId());
            payAppleUserRelationService.lambdaUpdate().eq(PayAppleUserRelation::getId, userRelation.getId())
                    .set(PayAppleUserRelation::getOriginalTransactionId, transaction.getOriginalTransactionId())
                    .update();
            log.info("用户订阅关系更新, appAccountToken: {} {}", appAccountToken, userRelation);
        }

        User userById = userService.getUserById(userRelation.getUserId());
        applePayService.handleInitialPurchase(transaction, userById, product);

    }

    private PayAppleUserRelation handleResubscribe(JWSTransactionDecodedPayload transaction, PayAppleProduct product) {
        log.info("处理重新订阅: originalTransactionId={}, transactionId={}", transaction.getOriginalTransactionId(), transaction.getTransactionId());
        PayAppleUserRelation userRelation = payAppleUserRelationService.queryRelationByOriginalTransactionId(transaction, () -> {
            return applePayService.getTransactionHistory(transaction, AUTO_RENEWABLE);
        });
        processResubscribeTransaction(userRelation, transaction, product);
        log.info("重新订阅处理完成: userId={}, originalTransactionId={}", userRelation != null ? userRelation.getUserId() : null, transaction.getOriginalTransactionId());
        return userRelation;
    }


    private void processResubscribeTransaction(PayAppleUserRelation userRelation, JWSTransactionDecodedPayload transaction, PayAppleProduct product) {
        if (userRelation == null) {
            log.error("无法处理重新订阅交易,用户关系不存在: originalTransactionId={}", transaction.getOriginalTransactionId());
            throw new RuntimeException("无法处理重新订阅交易,用户关系不存在");
        }
        log.info("处理重新订阅交易: userId={}, transactionId={}", userRelation.getUserId(), transaction.getTransactionId());

        try {
            // 获取用户信息
            User user = userService.getUserById(userRelation.getUserId());
            if (user == null) {
                log.error("用户不存在: userId={}", userRelation.getUserId());
                throw new RuntimeException("用户不存在");
            }
            // 保存交易记录
            appleTransactionService.saveTransaction(transaction, user);

            // 保存购买记录
            boolean successPurchase = payApplePurchaseRecordService.saveAppleLogicPurchaseRecord(transaction, user, Type.AUTO_RENEWABLE_SUBSCRIPTION);

            // 更新订阅状态
            if (successPurchase) {
                subscriptionCurrentService.saveOrUpdateAppleSubscriptionCurrent(buildAppleSubscriptionCurrent(transaction, user, product));
                // 更新用户VIP状态
                SubscriptionCurrent validSubscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
                userService.updateUserVipInfo(validSubscription, user.getId());
            }
            log.info("重新订阅交易处理完成: userId={}, transactionId={}", user.getId(), transaction.getTransactionId());
        } catch (Exception e) {
            log.error("处理重新订阅交易失败: userId={}, transactionId={}", userRelation.getUserId(), transaction.getTransactionId(), e);
            throw new RuntimeException("Failed to process resubscribe transaction", e);
        }
    }

    /**
     * 解码并验证签名的交易信息
     *
     * @param signedTransactionInfo 签名的交易信息
     * @return 解码后的交易信息
     */
    private String decodeAndVerify(String signedTransactionInfo) {
        try {
            DecodedJWT decodedJWT = JWT.decode(signedTransactionInfo);
            return decodedJWT.getPayload();
        } catch (Exception e) {
            throw new RuntimeException("Failed to decode and verify signedTransactionInfo", e);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
