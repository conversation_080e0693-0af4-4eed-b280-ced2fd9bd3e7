package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import com.lx.pl.pay.common.domain.PayLumenRecord;

import java.util.List;

/**
 * Lumen 记录服务接口
 */
public interface PayLumenRecordService extends IService<PayLumenRecord> {

    void saveLumenRecord(PayLogicPurchaseRecord payLogicPurchaseRecord);

    void recallLumen(Long payLogicPurchaseRecordId, String message);

    void saveOneTimeLumenRecord(List<PayLogicPurchaseRecord> payLogicPurchaseRecordList);

    void saveLumenRecordForApple(PayApplePurchaseRecord record);

    void saveOneTimeLumenRecordForApple(PayApplePurchaseRecord record);

    void restoreLumenByUserId(String originalTransactionId, Long newUserId, String loginName);

    void giftLumen(String loginName,Integer lumen);

    boolean hasPurchasedLumen(Long userId);

    // 可以添加一些自定义方法，例如：
    // List<PayLumenRecord> findByUserId(Long userId);
}
