package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("stripe_coupon")
@Schema(description = "Stripe 优惠券表")
public class StripeCoupon extends MyBaseEntity {

    @Schema(description = "数据库主键 ID")
    private Long id;

    @Schema(description = "Stripe 平台分配的优惠券唯一标识")
    private String stripeCouponId;

    @Schema(description = "优惠券名称（可选）")
    private String name;

    @Schema(description = "优惠持续时间类型：forever / once / repeating")
    private String duration;

    @Schema(description = "当 duration = repeating 时有效，表示优惠几个月")
    private Integer durationInMonths;

    @Schema(description = "例如：20 表示八折优惠，优惠 20%")
    private Integer percentOff;

    @Schema(description = "每个客户能兑换的次数")
    private Integer timesRedeemed;

    @Schema(description = "该优惠券的最大兑换总次数")
    private Integer maxRedemptions;

    @Schema(description = "截止兑换时间（UTC 时间）")
    private Integer redeemBy;

    @Schema(description = "是否有效：true / false")
    private Boolean valid;

    @Schema(description = "备注信息（可选）")
    private String mark;
} 