package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "当前订阅")
@TableName("subscription_current")
public class SubscriptionCurrent extends MyBaseEntity {

    /**
     * 记录 ID
     */
    @Schema(description = "记录 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * 平台唯一标识（如 Stripe 的 subscription_id 或 Apple 的 receipt_id）
     */
    @Schema(description = "平台唯一标识（如 Stripe 的 subscription_id 或 Apple 的 original）")
    private String subscriptionId;

    private String originalTransactionId;

    private String transactionId;

    /**
     * 订阅每周期开始时间
     */
    @Schema(description = "订阅每周期开始时间")
    private Long currentPeriodStart;

    /**
     * 订阅每周期结束时间
     */
    @Schema(description = "订阅每周期结束时间")
    private Long currentPeriodEnd;

    /**
     * 会员类型：standard 普通会员 pro 高级会员
     */
    @Schema(description = "会员类型： standard 普通会员 pro 高级会员")
    private String planLevel;

    /**
     * 订阅计费周期：year, month
     */
    @Schema(description = "订阅计费周期：year, month")
    private String priceInterval;

    /**
     * 会员生效时间
     */
    @Schema(description = "会员生效时间")
    private Long vipBeginTime;

    /**
     * 会员过期时间
     */
    @Schema(description = "会员过期时间")
    private Long vipEndTime;

    /**
     * 订阅来源平台：stripe，ios，android
     */
    @Schema(description = "订阅来源平台：stripe，ios，android")
    private String vipPlatform;

    /**
     * 自动续订状态（1 表示启用，0 表示关闭）
     */
    @Schema(description = "自动续订状态（1 表示启用，0 表示关闭）")
    private Integer autoRenewStatus;

    /**
     * 是否无效，存在取消的情况
     */
    @Schema(description = "是否无效，存在取消的情况")
    private Boolean invalid;

    private String renewPrice;

    private Boolean trial;

    private String mark;
}
