package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.common.domain.StripeCoupon;
import com.lx.pl.pay.common.mapper.StripeCouponMapper;
import com.lx.pl.pay.common.service.StripeCouponService;
import org.springframework.stereotype.Service;

@Service
public class StripeCouponServiceImpl extends ServiceImpl<StripeCouponMapper, StripeCoupon> implements StripeCouponService {
    @Override
    public StripeCoupon queryLumenSaleBySaleNum(Integer lumenDiscount) {
        return this.lambdaQuery()
                .eq(StripeCoupon::getPercentOff, lumenDiscount)
                .eq(StripeCoupon::getValid, true)
                .eq(StripeCoupon::getDuration, "forever")
                .isNull(StripeCoupon::getRedeemBy)
                .one();
    }
}