package com.lx.pl.pay.stripe.dto;

import com.stripe.param.checkout.SessionCreateParams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckoutSessionRequest {

    private String priceId;
    private String successUrl;
    private String cancelUrl;
    private String customerId;
    private String couponId;
    private String taxRateId;
    private Long count;
    private SessionCreateParams.Mode mode;

}
