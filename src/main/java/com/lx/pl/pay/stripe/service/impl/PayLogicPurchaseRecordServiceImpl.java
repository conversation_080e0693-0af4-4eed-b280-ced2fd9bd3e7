package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.mapper.PayLogicPurchaseRecordMapper;
import com.lx.pl.pay.stripe.service.PayLogicPurchaseRecordService;
import com.lx.pl.pay.stripe.service.StripeUserCustomerService;
import com.lx.pl.service.StripeProductService;
import com.lx.pl.service.VipService;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付逻辑购买记录服务实现类
 *
 * <AUTHOR>
 */
@Service
public class PayLogicPurchaseRecordServiceImpl extends ServiceImpl<PayLogicPurchaseRecordMapper, PayLogicPurchaseRecord> implements PayLogicPurchaseRecordService {

    Logger log = LoggerFactory.getLogger("stripe-pay-msg");

    @Resource
    private StripeProductService stripeProductService;

    @Resource
    private PayLumenRecordService payLumenRecordService;
    @Resource
    private StripeUserCustomerService stripeUserCustomerService;
    @Resource
    private VipService vipService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLogicPurchaseRecord(StripeSubscriptionRecord subscriptionRecord, String latestInvoice, StripeProduct stripeProduct, boolean isTrial) {
        log.info("start saveLogicPurchaseRecord {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
        PayLogicPurchaseRecord payLogicPurchaseRecord = this.lambdaQuery()
                .eq(PayLogicPurchaseRecord::getSubscriptionId, subscriptionRecord.getSubscriptionId())
                .eq(PayLogicPurchaseRecord::getCurrentPeriodStart, subscriptionRecord.getCurrentPeriodStart())
                .eq(PayLogicPurchaseRecord::getCurrentPeriodEnd, subscriptionRecord.getCurrentPeriodEnd())
                .eq(PayLogicPurchaseRecord::getCancel, false)
                .one();
        if (payLogicPurchaseRecord != null) {
            log.info("end2 saveLogicPurchaseRecord exists {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
            return false;
        }
        payLogicPurchaseRecord = buildPayLogicPurchaseRecord(subscriptionRecord, stripeProduct, latestInvoice, isTrial);
        this.save(payLogicPurchaseRecord);
        payLogicPurchaseRecord.setTrialDays(stripeProduct.getTrialDay() == null ? 0 : stripeProduct.getTrialDay());
        payLumenRecordService.saveLumenRecord(payLogicPurchaseRecord);
        log.info("end saveLogicPurchaseRecord {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
        return true;
    }

    @NotNull
    private PayLogicPurchaseRecord buildPayLogicPurchaseRecord(StripeSubscriptionRecord subscriptionRecord, StripeProduct one, String latestInvoice, boolean isTrial) {
        PayLogicPurchaseRecord payLogicPurchaseRecord = new PayLogicPurchaseRecord();
        payLogicPurchaseRecord.setCreateTime(LocalDateTime.now());
        payLogicPurchaseRecord.setUserId(subscriptionRecord.getUserId());
        payLogicPurchaseRecord.setLoginName(subscriptionRecord.getLoginName());
        payLogicPurchaseRecord.setCustomerId(subscriptionRecord.getCustomerId());
        payLogicPurchaseRecord.setPriceId(subscriptionRecord.getPriceId());
        payLogicPurchaseRecord.setInvoiceId(latestInvoice);
        payLogicPurchaseRecord.setCurrentPeriodStart(subscriptionRecord.getCurrentPeriodStart());
        payLogicPurchaseRecord.setCurrentPeriodEnd(subscriptionRecord.getCurrentPeriodEnd());
        payLogicPurchaseRecord.setLogicPeriodStart(subscriptionRecord.getLogicPeriodStart());
        payLogicPurchaseRecord.setLogicPeriodEnd(subscriptionRecord.getLogicPeriodEnd());
        payLogicPurchaseRecord.setLumenQty(isTrial ? one.getInitialLumen() : one.getLumen());
        payLogicPurchaseRecord.setSubscriptionId(subscriptionRecord.getSubscriptionId());
        payLogicPurchaseRecord.setVipPlatForm(VipPlatform.STRIPE.getPlatformName());
        if (!isTrial && "year".equals(subscriptionRecord.getSubInterval())) {
            payLogicPurchaseRecord.setCount(12);
        } else {
            payLogicPurchaseRecord.setCount(1);
        }
        payLogicPurchaseRecord.setTrial(isTrial);
        return payLogicPurchaseRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recallLogicPurchaseRecord(StripeSubscriptionRecord subscriptionRecord, String latestInvoice, String cancelReason) {
        log.info("start recallLogicPurchaseRecord {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
        PayLogicPurchaseRecord one = this.lambdaQuery()
                .eq(PayLogicPurchaseRecord::getSubscriptionId, subscriptionRecord.getSubscriptionId())
                .eq(PayLogicPurchaseRecord::getCurrentPeriodStart, subscriptionRecord.getCurrentPeriodStart())
                .eq(PayLogicPurchaseRecord::getCurrentPeriodEnd, subscriptionRecord.getCurrentPeriodEnd())
                .eq(PayLogicPurchaseRecord::getCancel, false)
                .one();
        if (one == null) {
            log.info("payLogicPurchaseRecord not exists {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
            return false;
        }
        this.lambdaUpdate().eq(PayLogicPurchaseRecord::getId, one.getId())
                .set(PayLogicPurchaseRecord::getCancel, true)
                .set(PayLogicPurchaseRecord::getUpdateTime, LocalDateTime.now())
                .update();
        // 处理paylumenRecord记录
        payLumenRecordService.recallLumen(one.getId(), cancelReason);
        log.info("end recallLogicPurchaseRecord {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneTimeLumen(Map<String, Integer> priceQtyMap, String customer, String invoiceId) {
        log.info("start saveOneTimeLumen {} {} {}", customer, invoiceId, priceQtyMap);
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, customer).one();

        List<PayLogicPurchaseRecord> payLogicPurchaseRecordList = new ArrayList<>(priceQtyMap.size());
        Map<String, Integer> priceFirstLumenMap = new HashMap<>();
        priceQtyMap.forEach((priceId, quantity) -> {
            PayLogicPurchaseRecord existRecord = this.lambdaQuery()
                    .eq(PayLogicPurchaseRecord::getPriceId, priceId)
                    .eq(PayLogicPurchaseRecord::getInvoiceId, invoiceId)
                    .eq(PayLogicPurchaseRecord::getCount, quantity)
                    .eq(PayLogicPurchaseRecord::getCustomerId, customer)
                    .one();
            if (existRecord != null) {
                log.info("payLogicPurchaseRecord already exists saveOneTimeLumen {} {} {} ", customer, invoiceId, priceId);
            } else {
                PayLogicPurchaseRecord payLogicPurchaseRecord = new PayLogicPurchaseRecord();
                payLogicPurchaseRecord.setCreateTime(LocalDateTime.now());
                payLogicPurchaseRecord.setUserId(userCustomer.getUserId());
                payLogicPurchaseRecord.setLoginName(userCustomer.getLoginName());
                payLogicPurchaseRecord.setCustomerId(customer);
                payLogicPurchaseRecord.setPriceId(priceId);
                payLogicPurchaseRecord.setInvoiceId(invoiceId);
                StripeProduct one = stripeProductService.lambdaQuery()
                        .eq(StripeProduct::getStripePriceId, priceId)
                        .one();
                if (one == null) {
                    throw new RuntimeException("StripeProduct not found");
                }
                priceFirstLumenMap.put(priceId, one.getInitialLumen());
                payLogicPurchaseRecord.setLumenQty(one.getLumen());
                payLogicPurchaseRecord.setCount(quantity);
                payLogicPurchaseRecord.setVipPlatForm(VipPlatform.STRIPE.getPlatformName());
                payLogicPurchaseRecord.setTrial(false);
                payLogicPurchaseRecordList.add(payLogicPurchaseRecord);
            }
        });
        if (!payLumenRecordService.hasPurchasedLumen(userCustomer.getUserId())) {
            priceFirstLumenMap.forEach((priceId, initialLumen) -> {
                PayLogicPurchaseRecord payLogicPurchaseRecord = new PayLogicPurchaseRecord();
                payLogicPurchaseRecord.setCreateTime(LocalDateTime.now());
                payLogicPurchaseRecord.setUserId(userCustomer.getUserId());
                payLogicPurchaseRecord.setLoginName(userCustomer.getLoginName());
                payLogicPurchaseRecord.setCustomerId(customer);
                payLogicPurchaseRecord.setPriceId(priceId);
                payLogicPurchaseRecord.setInvoiceId(null);
                payLogicPurchaseRecord.setLumenQty(initialLumen);
                payLogicPurchaseRecord.setCount(1);
                payLogicPurchaseRecord.setVipPlatForm(VipPlatform.STRIPE.getPlatformName());
                payLogicPurchaseRecord.setTrial(false);
                payLogicPurchaseRecordList.add(payLogicPurchaseRecord);
            });
        }
        if (!payLogicPurchaseRecordList.isEmpty()) {
            this.saveBatch(payLogicPurchaseRecordList);
            payLumenRecordService.saveOneTimeLumenRecord(payLogicPurchaseRecordList);
            log.info("end saveOneTimeLumen {} {} {}", customer, invoiceId, priceQtyMap);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelLastRecord(String priceId, String subscriptionId, long nowTime) {
        log.info("start cancelLastRecord subscriptionId: {} priceId: {} nowTime: {}", subscriptionId, priceId, nowTime);
        // 根据原来的priceId 查询购买记录
        PayLogicPurchaseRecord payLogicPurchaseRecord = this.lambdaQuery()
                .eq(PayLogicPurchaseRecord::getPriceId, priceId)
                .eq(PayLogicPurchaseRecord::getSubscriptionId, subscriptionId)
                .eq(PayLogicPurchaseRecord::getCancel, false)
                .gt(PayLogicPurchaseRecord::getCurrentPeriodEnd, nowTime)
                .lt(PayLogicPurchaseRecord::getCurrentPeriodStart, nowTime)
                .orderByDesc(PayLogicPurchaseRecord::getId)
                .last("limit 1")
                .one();

        if (payLogicPurchaseRecord != null) {
            payLumenRecordService.recallLumen(payLogicPurchaseRecord.getId(), "stripe upgrade");
            this.lambdaUpdate().eq(PayLogicPurchaseRecord::getId, payLogicPurchaseRecord.getId())
                    .set(PayLogicPurchaseRecord::getCancel, true)
                    .set(PayLogicPurchaseRecord::getUpdateTime, LocalDateTime.now())
                    .update();

            log.info("end cancelLastRecord subscriptionId: {} priceId: {} nowTime: {}", subscriptionId, priceId, nowTime);
        }
    }

    @Override
    public boolean saveLogicPurchaseRecordForUpgrade(StripeSubscriptionRecord subscriptionRecord, String latestInvoice, StripeProduct stripeProduct) {
        log.info("start saveLogicPurchaseRecordForUpgrade {} {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId(), latestInvoice);
        PayLogicPurchaseRecord payLogicPurchaseRecord = this.lambdaQuery()
                .eq(PayLogicPurchaseRecord::getSubscriptionId, subscriptionRecord.getSubscriptionId())
                .eq(PayLogicPurchaseRecord::getCurrentPeriodStart, subscriptionRecord.getCurrentPeriodStart())
                .eq(PayLogicPurchaseRecord::getCurrentPeriodEnd, subscriptionRecord.getCurrentPeriodEnd())
                .eq(PayLogicPurchaseRecord::getCancel, false)
                .one();
        if (payLogicPurchaseRecord != null) {
            log.info("end2 saveLogicPurchaseRecordForUpgrade exists {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
            return false;
        }
        payLogicPurchaseRecord = buildPayLogicPurchaseRecord(subscriptionRecord, stripeProduct, latestInvoice, false);
        this.save(payLogicPurchaseRecord);
        payLumenRecordService.saveLumenRecord(payLogicPurchaseRecord);
        log.info("end saveLogicPurchaseRecordForUpgrade {} {}", subscriptionRecord.getCustomerId(), subscriptionRecord.getSubscriptionId());
        return true;
    }
}
