package com.lx.pl.pay.stripe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.stripe.domain.PayLogicPurchaseRecord;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;

import java.util.Map;

/**
 * 支付逻辑购买记录服务接口
 */
public interface PayLogicPurchaseRecordService extends IService<PayLogicPurchaseRecord> {
    /**
     * 保存逻辑购买记录
     *
     * @param subscriptionRecord
     * @param latestInvoice
     * @param stripeProduct
     * @param isTrial
     * @return
     */
    boolean saveLogicPurchaseRecord(StripeSubscriptionRecord subscriptionRecord, String latestInvoice, StripeProduct stripeProduct, boolean isTrial);

    /**
     * 撤回逻辑购买记录
     *
     * @param subscriptionRecord
     * @param latestInvoice
     * @param cancelReason
     * @return
     */
    boolean recallLogicPurchaseRecord(StripeSubscriptionRecord subscriptionRecord, String latestInvoice, String cancelReason);

    void saveOneTimeLumen(Map<String, Integer> priceQtyMap, String customer, String id);

    void cancelLastRecord(String priceId, String subscriptionId, long nowTime);

    boolean saveLogicPurchaseRecordForUpgrade(StripeSubscriptionRecord one, String latestInvoice, StripeProduct stripeProduct);

    // 可以添加一些自定义方法，例如：
    // List<PayLogicPurchaseRecord> findByUserId(Long userId);
}
