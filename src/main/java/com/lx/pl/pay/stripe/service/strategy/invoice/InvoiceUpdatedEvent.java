package com.lx.pl.pay.stripe.service.strategy.invoice;

import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeInvoice;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.service.StripeInvoiceService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.Address;
import com.stripe.model.Invoice;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.lx.pl.pay.PayConstant.INVOICE_LOCK_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = {"invoice.updated", "invoice.voided"})
public class InvoiceUpdatedEvent extends IStripeEventHandler<Invoice> {

    @Resource
    private StripeInvoiceService stripeInvoiceService;

    @Override
    public void handleEvent(Invoice event, String eventId) {
        String id = event.getId();
        String lockKey = INVOICE_LOCK_PREFIX + id;
        log.info("start invoice.updated: {} {} {}", eventId, System.currentTimeMillis(), lockKey);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock();
            applicationContext.getBean(InvoiceUpdatedEvent.class)
                    .doHandleEvent(event, id);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock invoice.updated: {} {} {}", eventId, System.currentTimeMillis(), lockKey);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandleEvent(Invoice event, String id) {
        log.info("doHandleEvent invoice.updated: {} {}", System.currentTimeMillis(), id);
        StripeInvoice one = stripeInvoiceService.lambdaQuery()
                .eq(StripeInvoice::getInvoiceId, id)
                .one();
        if (one == null) {
            StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                    .eq(StripeUserCustomer::getCustomerId, event.getCustomer())
                    .one();
            StripeInvoice invoice = buildStripeInvoice(event, userCustomer);
            stripeInvoiceService.save(invoice);
            // 订单完成的处理逻辑
            log.info("save invoice.updated : {} customer: {}", event.getId(), event.getCustomer());
        } else {
            if (!InvoiceUtil.isTerminalState(one)) {
                stripeInvoiceService.lambdaUpdate()
                        .eq(StripeInvoice::getInvoiceId, id)
                        .set(StripeInvoice::getStatus, event.getStatus())
                        .set(StripeInvoice::getAmountPaid, event.getAmountPaid())
                        .set(StripeInvoice::getAmountRemaining, event.getAmountRemaining())
                        .set(StripeInvoice::getUpdateTime, LocalDateTime.now())
                        .update();
            }

        }
    }


    @NotNull
    static StripeInvoice buildStripeInvoice(Invoice event, StripeUserCustomer userCustomer) {
        StripeInvoice invoice = new StripeInvoice();
        invoice.setInvoiceId(event.getId());
        invoice.setCustomerId(event.getCustomer());
        invoice.setAmountDue(event.getAmountDue());
        invoice.setAmountRemaining(event.getAmountRemaining());
        invoice.setAmountPaid(event.getAmountPaid());
        if (userCustomer != null) {
            invoice.setUserId(userCustomer.getUserId());
            invoice.setLoginName(userCustomer.getLoginName());
        }
        invoice.setBillingReason(event.getBillingReason());
        invoice.setCurrency(event.getCurrency());
        invoice.setHostedInvoiceUrl(event.getHostedInvoiceUrl());
        invoice.setInvoicePdf(event.getInvoicePdf());

        invoice.setSubscriptionId(event.getSubscription());
        invoice.setPaymentIntentId(event.getPaymentIntent());
        invoice.setStatus(event.getStatus());
        invoice.setCreateTime(LocalDateTime.now());

        invoice.setTotal(event.getTotal());
        invoice.setTotalExcludingTax(event.getTotalExcludingTax());

        Address customerAddress = event.getCustomerAddress();
        if (customerAddress != null) {
            invoice.setCustomerCountry(customerAddress.getCountry());
        }
        return invoice;
    }

}