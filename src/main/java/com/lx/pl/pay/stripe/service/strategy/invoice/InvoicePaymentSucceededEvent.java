package com.lx.pl.pay.stripe.service.strategy.invoice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeInvoice;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.service.PayLogicPurchaseRecordService;
import com.lx.pl.pay.stripe.service.StripeInvoiceService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.Invoice;
import com.stripe.model.InvoiceLineItem;
import com.stripe.model.InvoiceLineItemCollection;
import com.stripe.model.Price;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lx.pl.pay.PayConstant.INVOICE_LOCK_PREFIX;
import static com.lx.pl.pay.stripe.service.strategy.invoice.InvoicePaymentFailedEvent.buildStripeInvoice;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = {"invoice.payment_succeeded", "invoice.paid"})
public class InvoicePaymentSucceededEvent extends IStripeEventHandler<Invoice> {

    @Resource
    private StripeInvoiceService stripeInvoiceService;

    @Resource
    private PayLogicPurchaseRecordService paymentLogicPurchaseRecordService;

    @Override
    public void handleEvent(Invoice event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(INVOICE_LOCK_PREFIX + id);
        log.info("start invoice.payment_succeeded: {} {} {}", event.getCustomer(), eventId, INVOICE_LOCK_PREFIX + id);
        try {
            lock.lock();
            String loginName = applicationContext.getBean(InvoicePaymentSucceededEvent.class)
                    .doHandleEvent(event, id);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
                vipService.clearTrialFirst(loginName);
            }
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock invoice.payment_succeeded: {} {} {}", event.getCustomer(), eventId, INVOICE_LOCK_PREFIX + id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String doHandleEvent(Invoice event, String id) {
        log.info("start doHandleEvent invoice.payment_succeeded: {} {}", event.getCustomer(), id);
        StripeInvoice one = stripeInvoiceService.lambdaQuery()
                .eq(StripeInvoice::getInvoiceId, id)
                .one();
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer())
                .one();
        if (one == null) {
            StripeInvoice invoice = buildStripeInvoice(event, userCustomer);
            stripeInvoiceService.save(invoice);
            // 订单完成的处理逻辑
            log.info("save invoice.payment_succeeded: {} {}", event.getCustomer(), event.getId());
        } else {
            if (!InvoiceUtil.isTerminalState(one)) {
                stripeInvoiceService.lambdaUpdate()
                        .eq(StripeInvoice::getInvoiceId, id)
                        .set(StripeInvoice::getStatus, event.getStatus())
                        .set(StripeInvoice::getAmountPaid, event.getAmountPaid())
                        .set(StripeInvoice::getAmountDue, event.getAmountDue())
                        .set(StripeInvoice::getAmountRemaining, event.getAmountRemaining())
                        .set(StripeInvoice::getUpdateTime, LocalDateTime.now()).
                        update();
                log.info("exist invoice.payment_succeeded: {} {} {} ", event.getCustomer(), event.getId(), event.getStatus());
            } else {
                log.info("isTerminalState invoice.payment_succeeded: {} {} {}", event.getCustomer(), event.getId(), event.getStatus());
            }
        }
        InvoiceLineItemCollection lines = event.getLines();
        if (lines != null) {
            if (CollUtil.isNotEmpty(lines.getData())) {
                List<InvoiceLineItem> data = lines.getData();
                Map<String, Integer> priceQtyMap = new HashMap<>();
                for (InvoiceLineItem invoiceLineItem : data) {
                    Price price = invoiceLineItem.getPrice();
                    if (price != null && "one_time".equals(price.getType())) {
                        String priceId = price.getId();
                        int quantity = Integer.parseInt(invoiceLineItem.getQuantity()
                                .toString());
                        priceQtyMap.put(priceId, quantity);
                    }
                }
                if (CollUtil.isNotEmpty(priceQtyMap)) {
                    paymentLogicPurchaseRecordService.saveOneTimeLumen(priceQtyMap, event.getCustomer(), event.getId());
                }
            }
            log.info("end doHandleEvent invoice.payment_succeeded: {} {}", event.getCustomer(), id);
        }
        return userCustomer.getLoginName();

    }
}