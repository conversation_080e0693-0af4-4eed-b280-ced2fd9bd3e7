package com.lx.pl.pay.stripe.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.common.annotation.PayLogContext;
import com.lx.pl.pay.stripe.dto.SubscriptionScheduleDTO;
import com.lx.pl.pay.stripe.service.StripePayService;
import com.lx.pl.pay.stripe.dto.BuyItemDto;
import com.lx.pl.pay.stripe.service.StripeSubscriptionRecordService;
import com.lx.pl.service.StripeProductService;
import com.stripe.model.SubscriptionSchedule;
import com.stripe.model.checkout.Session;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "stripe购买相关接口")
@RequestMapping("/api/stripe/pay")
@RequiredArgsConstructor
public class StripeController {

    @Resource
    private StripePayService stripePayService;
    @Autowired
    private StripeProductService stripeProductService;
    @Autowired
    private StripeSubscriptionRecordService stripeSubscriptionRecordService;

    @Operation(summary = "购买或订阅产品")
    @PostMapping("/create-payment")
    @Authorization
    @PayLogContext("create-payment-")
    public R<String> createPayment(@RequestBody @Parameter(description = "订阅相关参数对象") BuyItemDto buyItemDto,
                                   @CurrentUser @Parameter(hidden = true) User user) {
        Session session = stripePayService.createPayment(buyItemDto, user);
        return R.success(session.getUrl());
    }

    @Operation(summary = "升级订阅")
    @PostMapping("/upgradeSubscription")
    @Authorization
    @PayLogContext("upgradeSubscription-")
    public R<String> upgradeSubscription(@RequestBody @Parameter(description = "订阅相关参数对象") BuyItemDto buyItemDto,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        stripePayService.upgradeSubscription(buyItemDto, user);
        return R.success();
    }

    @Operation(summary = "取消订阅自动续费")
    @PostMapping("/cancel-future")
    @Authorization
    @PayLogContext("cancel-future-")
    public R<String> cancelSubscriptionFuture(@CurrentUser @Parameter(hidden = true) User user) {
        stripePayService.cancelSubscriptionFuture(user);
        return R.success();
    }

    @Operation(summary = "恢复订阅自动续费")
    @PostMapping("/uncancel")
    @Authorization
    @PayLogContext("uncancel-")
    public R<String> uncancelSubscriptionFuture(@CurrentUser @Parameter(hidden = true) User user) {
        stripePayService.uncancelSubscriptionFuture(user);
        return R.success();
    }

    @Operation(summary = "改变未来订阅")
    @PostMapping("/changeSubscription")
    @Authorization
    @PayLogContext("changeSubscription-")
    public R<SubscriptionScheduleDTO> changeSubscription(@RequestBody @Parameter(description = "订阅相关参数对象") BuyItemDto buyItemDto,
                                                         @CurrentUser @Parameter(hidden = true) User user) {
        SubscriptionScheduleDTO subscriptionScheduleDTO = stripePayService.changeSubscriptionDelay(buyItemDto, user);
        return R.success(subscriptionScheduleDTO);
    }

    @Operation(summary = "获取还未生效的未来订阅")
    @PostMapping("/getSubscriptionSchedule")
    @Authorization
    @PayLogContext("getSubscriptionSchedule-")
    public R<SubscriptionScheduleDTO> findNotStartedSubscriptionSchedule(@CurrentUser @Parameter(hidden = true) User user) {
        try {
            SubscriptionSchedule notStartedSubscriptionSchedule = stripePayService.findNotStartedSubscriptionSchedule(user);
            if (notStartedSubscriptionSchedule == null) {
                return R.success();
            }
            List<SubscriptionSchedule.Phase> phases = notStartedSubscriptionSchedule.getPhases();
            // 先检查 phases 是否为空
            if (phases == null || phases.isEmpty()) {
                return R.success(); // 如果没有 phase，直接返回成功
            }
            // 获取第一个阶段
            SubscriptionSchedule.Phase firstPhase = phases.get(0);
            if (firstPhase == null || firstPhase.getItems() == null || firstPhase.getItems().isEmpty()) {
                return R.success(); // 如果第一个阶段的 items 为空，返回成功
            }
            // 获取第一个项目的价格信息
            String price = firstPhase.getItems().get(0).getPrice();
            if (price == null || price.isEmpty()) {
                return R.success(); // 如果价格为空，返回成功
            }
            StripeProduct stripeProduct = stripeProductService.getStripeProductByPriceId(price);
            Long startDateTimestamp = firstPhase.getStartDate(); // 获取 Unix 时间戳（秒）
            return R.success(new SubscriptionScheduleDTO(
                    stripeProduct.getPlanLevel(),
                    stripeProduct.getPriceInterval(),
                    startDateTimestamp
            ));
        } catch (Exception e) {
            return R.success();
        }
    }

    @Operation(summary = "取消还未生效的未来订阅")
    @PostMapping("/cancelSubscriptionSchedule")
    @Authorization
    @PayLogContext("cancelSubscriptionSchedule-")
    public R<String> cancelSubscriptionSchedule(@CurrentUser @Parameter(hidden = true) User user) {
        stripePayService.cancelSubscriptionSchedule(user);
        return R.success();
    }

    @Operation(summary = "生成账单")
    @GetMapping("/billing")
    @Authorization
    @PayLogContext("billing-")
    public R<String> billing(@RequestParam @Parameter(description = "点击返回时的url") String returnUrl, @CurrentUser @Parameter(hidden = true) User user) {
        com.stripe.model.billingportal.Session session = stripePayService.createPortal(user, returnUrl);
        return R.success(session.getUrl());
    }

    @Operation(summary = "发票和payment状态校对")
    @GetMapping("/checkInvoiceAndPayment")
    @PayLogContext("checkInvoiceAndPayment-")
    public R<String> checkInvoice() {
        long start = System.currentTimeMillis();
        stripePayService.checkInvoiceAndPayment();
        return R.success("校对成功，耗时：" + (System.currentTimeMillis() - start) / 1000D + "s");

    }

    @GetMapping("/fixVip")
    @PayLogContext("fixVip-")
    @Authorization
    public R<String> getSubscription() {
        stripeSubscriptionRecordService.fixVip();
        return R.success("成功");
    }
}
