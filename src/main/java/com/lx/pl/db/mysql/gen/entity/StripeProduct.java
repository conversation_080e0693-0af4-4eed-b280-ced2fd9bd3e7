package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Stripe Product Entity
 */
@Data
@TableName("stripe_product")
public class StripeProduct extends MyBaseEntity {
    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * Stripe 生成的产品 ID
     */
    @Schema(description = "Stripe 生成的产品 ID")
    private String stripeProductId;

    /**
     * Stripe 生成的价格 ID
     */
    @Schema(description = "Stripe 生成的价格 ID")
    private String stripePriceId;

    /**
     * lumen 数量/month
     */
    @Schema(description = "订阅： 每月lumen数量， one time 一次性发放数量")
    private Integer lumen;

    /**
     * 试用天数
     */
    @Schema(description = "试用天数")
    private Integer trialDay;

    /**
     * 试用赠送lumen数
     */
    @Schema(description = "试用赠送lumen数")
    private Integer initialLumen;

    @Schema(description = "首充订阅折扣")
    private Integer firstBuySubDiscount;

    /**
     * 计划等级
     */
    @Schema(description = "计划等级（例如：standard，pro）")
    private String planLevel;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型（例如：plan，one）")
    private String productType;

    /**
     * 价格间隔
     */
    @Schema(description = "价格间隔（例如：year，month）")
    private String priceInterval;

    /**
     * vip 等級
     */
    @Schema(description = "vip 等級")
    private Integer vipLevel;

    /**
     * 商品描述信息
     */
    @Schema(description = "商品描述信息")
    private String mark;

    @Schema(description = "状态（0：禁用，1：启用）")
    private Integer status;

    private String price;
}
