package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Apple Product Entity
 */
@Data
@TableName("pay_apple_product")
public class PayAppleProduct extends MyBaseEntity {
    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * apple 生成的产品ID
     */
    @Schema(description = "apple 生成的产品ID")
    private String appleProductId;

    /**
     * lumen 数量/month
     */
    @Schema(description = "订阅： 每月lumen数量， one time 一次性发放数量")
    private Integer lumen;

    private Integer initialLumen;

    /**
     * 计划等级
     */
    @Schema(description = "计划等级（例如：standard，pro）")
    private String planLevel;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型（例如：plan，one）")
    private String productType;

    /**
     * 价格间隔
     */
    @Schema(description = "价格间隔（例如：year，month）")
    private String priceInterval;

    /**
     * vip 等級
     */
    @Schema(description = "vip 等級")
    private Integer vipLevel;

    /**
     * 商品描述信息
     */
    @Schema(description = "商品描述信息")
    private String mark;

    private Boolean status;
}
