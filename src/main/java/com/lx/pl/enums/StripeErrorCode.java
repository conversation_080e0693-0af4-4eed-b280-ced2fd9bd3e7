package com.lx.pl.enums;

/**
 * @Description: stripe支付处理枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2024/12/27
 */
public enum StripeErrorCode {
    GET_SUBSCRIPTION_SCHEDULES_ERROR("710", "Failed to get subscription schedules.", "获取订阅计划异常"),
    GET_ACTIVE_SUBSCRIPTION_ERROR("711", "Failed to get active subscription.", "获取激活订阅异常"),
    CREATE_CHECKOUT_SESSION_ERROR("712", "Failed to create checkout session .", "创建支付会话异常"),
    SUBSCRIPTION_NOT_FOUND("713", "Subscription not found.", "没有订阅"),
    CREATE_SUBSCRIPTION_SCHEDULE_ERROR("714", "Failed to create subscription schedule.", "创建订阅计划异常"),
    CANCEL_SUBSCRIPTION_DELAY_ERROR("715", "Failed to cancel subscription.", "延迟取消订阅异常"),
    CANCEL_SUBSCRIPTION_SCHEDULE_ERROR("716", "Failed to cancel subscription schedule.", "取消订阅计划异常"),
    CREATE_STRIPE_CUSTOMER_ERROR("717", "Failed to create stripe customer.", "创建stripe支付用户customer失败"),
    CREATE_NEW_SUBSCRIPTION_FAILED("718", "Failed to create new subscription.", "创建新订阅失败"),
    NO_STRIPE_CUSTOMER_FOUND("719", "Invalid parameters.", "通过用户在db中找不到对应的customer"),
    NO_STRIPE_PRODUCT_FOUND("720", "Invalid parameters.", "没有找到db对应的StripeProduct数据"),
    UNSUPPORTED_PAYMENT_TYPE("721", "Invalid parameters.", "不支持的payment_type，只支持one和plan"),
    FAILED_TO_GET_BILLING("722", "Failed to get billing", "获取账单失败"),
    CANNOT_UPGRADE_SUBSCRIPTION("723", "Cannot upgrade subscription", "升级规则不满足，不能切换指定订阅"),
    API_USAGE_ERROR("724", "Bad request.", "只能同时有一个订阅生效，不能再立即够买其他订阅，接口调用错误"),
    NO_PAYMENT_METHOD_SAVED("725", "The customer has no payment method saved, so a subscription cannot be created.", "客户没有保存任何支付方式，无法创建订阅"),
    HAVE_ACTIVE_SUBSCRIPTION_SCHEDULE("726", "An active subscription plan was found, and future subscriptions cannot be switched for the time being.", "发现 active 订阅计划，暂时不能切换未来订阅"),
    CANCEL_SUBSCRIPTION_ERROR("727", "Failed to cancel subscription.", "立即取消订阅异常"),
    SUBSCRIPTION_NOT_PENDING_CANCEL("728", "Failed to uncancel subscription.", "当前激活订阅并未标记为取消，不能uncancel"),
    UNCANCEL_SUBSCRIPTION_ERROR("729", "Failed to uncancel subscription.", "调用stripe取消报错"),
    CROSS_PLATFORM_ERROR("730", "Cannot make cross-platform payments.", "不能够跨平台支付"),
    HAVE_ACTIVE_SUBSCRIPTION_SCHEDULE_TO_CANCEL("731", "An active subscription plan was found, and future subscriptions cannot be cancel for the time being.", "发现 active 订阅计划，暂时不能取消未来订阅"),
    HAVE_ACTIVE_OR_NOSTART_SUBSCRIPTION_SCHEDULE_TO_UNCANCEL("732", "An active or no start subscription plan was found, plan cannot uncancel.", "发现 active或者no start 订阅计划，暂时不能恢复自动订阅"),
    SUBSCRIPTION_REQUIRED("750", "You must subscribe before purchasing Lumen.", "订阅后才能购买"),
    ACTIVE_SUBSCRIPTION_REQUIRED("751", "You must subscribe before.", "没有激活的请阅"),
    UPDATE_SUBSCRIPTION_ITEM_PRICE_ERROR("752", "update subscription item price error.", "修改订阅项价格异常"),
    STRIPE_SET_DEFAULT_PAYMENT_METHOD_ERROR("771", "set default payment method error.", "调用stripe设置客户默认支付方式失败"),
    STRIPE_CREATE_NEW_SUBSCRIPTION_ERROR("772", "failed to create new subscription.", "调用stripe创建新订阅失败"),
    STRIPE_GET_CUSTOMER_ERROR("773", "failed to get customer.", "调用stripe获取客户信息失败"),
    STRIPE_GET_PAYMENT_METHOD_ERROR("774", "failed to get pay method.", "调用stripe获取客户支付方式失败"),
    STRIPE_CANCEL_SUBSCRIPTION_NOW_ERROR("775", "failed to get pay method.", "调用stripe获取客户支付方式失败"),
    GENERIC_ERROR("799", "A generic error occurred during payment processing.", "支付处理发生错误");

    private final String code;
    private final String message;
    private final String description;

    StripeErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
