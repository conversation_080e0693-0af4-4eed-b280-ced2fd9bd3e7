package com.lx.pl;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@Slf4j
@MapperScan({"com.lx.pl.db.mysql.**.mapper", "com.lx.pl.pay.apple.mapper", "com.lx.pl.pay.stripe.mapper", "com.lx.pl.pay.common.mapper"})
@EnableAspectJAutoProxy
@EnableAsync
public class PicLumenApplication {

    public static void main(String[] args) throws ClassNotFoundException {
        Class.forName("org.apache.shardingsphere.sql.parser.core.database.parser.DatabaseTypedSQLParserFacadeFactory");
        Class.forName("org.apache.shardingsphere.sql.parser.core.database.visitor.SQLVisitorFacadeFactory");
//        Class.forName("org.apache.shardingsphere.infra.executor.sql.prepare.driver.SQLExecutionUnitBuilderFactory");
        SpringApplication.run(PicLumenApplication.class, args);
    }


}
