package com.lx.pl.job;


import com.lx.pl.lb.DealTaskScheduler;
import com.lx.pl.service.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class XxlJobTask {

    private static final Logger logger = LoggerFactory.getLogger("schedule-task");

    @Value("${statistics.kpi}")
    Boolean statisticsKpi;

    @Autowired
    StatisticsService statisticsService;

    @Autowired
    ResettingUserMessageService messageService;

    @Autowired
    LoadBalanceService loadBalanceService;

    @Autowired
    EventLogService eventLogService;

    @Autowired
    DealTaskScheduler dealTaskScheduler;
    @Autowired
    private FileScoreService fileScoreService;

    @XxlJob("resettingImgNumTask")
    public ReturnT resettingImgNum() {
        logger.info("重置用户当日生图数量任务");
        try {
            messageService.resettingUserCreateImgNumTask();
            messageService.resettingUserPublicImgNum();
            messageService.resettingUserLumens();
        } catch (Exception e) {
            logger.error("重置用户当日生图数量报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("dealProcessTask")
    public ReturnT dealProcess() {
        try {
            loadBalanceService.senQueueIndex();
        } catch (Exception e) {
            logger.error("任务队列发送排队信息报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("loadBalanceConsumerTask")
    public ReturnT loadBalanceConsumer() {
        try {
            dealTaskScheduler.processTasks2();
        } catch (Exception e) {
            logger.error("消费任务队列报错", e);
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("dealRelaxWaitQueueTask")
    public ReturnT dealRelaxWaitQueue() {
        try {
            loadBalanceService.sendRelaxWaitQueue();
        } catch (Exception e) {
            logger.error("relax等待队列发送非公平排队队列报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("flushFileScore")
    public ReturnT flushFileScore() {
        try {
            fileScoreService.updateFileScore();
        } catch (Exception e) {
            logger.error("flushFileScore报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("initFlushData")
    public ReturnT initFlushData() {
        try {
            fileScoreService.initData();
        } catch (Exception e) {
            logger.error("initadata error", e);
        }
        return ReturnT.SUCCESS;
    }
}
