<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>piclumen</artifactId>
    <build>
        <plugins>
            <plugin>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <artifactId>lombok</artifactId>
                            <groupId>org.projectlombok</groupId>
                        </exclude>
                    </excludes>
                </configuration>
                <groupId>org.springframework.boot</groupId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
                <groupId>org.apache.maven.plugins</groupId>
            </plugin>
            <!-- 自定义ClassPath路径 -->
                                    <plugin>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-jar-plugin</artifactId>
                                        <configuration>
                                            <archive>
                                                <manifest>
                                                    <addClasspath>true</addClasspath>
                                                    <classpathPrefix>lib/</classpathPrefix>
                                                </manifest>
                                                <manifestEntries>
                                                    <Class-Path>./</Class-Path>
                                                </manifestEntries>
                                            </archive>
                                        </configuration>
                                    </plugin>
                                    <plugin>
                                        <groupId>org.springframework.boot</groupId>
                                        <artifactId>spring-boot-maven-plugin</artifactId>
                                        <configuration>
                                            <fork>true</fork>
                                            <addResources>true</addResources>
                                            <includes>
                                                <include>
                                                    <groupId>not-exists</groupId>
                                                </include>
                                            </includes>
                                        </configuration>
                                    </plugin>
                                    <plugin>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-dependency-plugin</artifactId>
                                        <executions>
                                            <execution>
                                                <id>copy-dependencies</id>
                                                <phase>prepare-package</phase>
                                                <goals>
                                                    <goal>copy-dependencies</goal>
                                                </goals>
                                                <configuration>
                                                    <outputDirectory>${project.build.directory}/lib</outputDirectory>
                                                </configuration>
                                            </execution>
                                        </executions>
                                    </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.apple.itunes.storekit</groupId>
            <artifactId>app-store-server-library</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-web</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <!-- knife4j -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <groupId>com.baomidou</groupId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <artifactId>mybatis-plus-generator</artifactId>
            <groupId>com.baomidou</groupId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <artifactId>velocity-engine-core</artifactId>
            <groupId>org.apache.velocity</groupId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <artifactId>mysql-connector-java</artifactId>
            <groupId>mysql</groupId>
            <version>8.0.29</version>
        </dependency>
        <dependency>
            <artifactId>wechatpay-apache-httpclient</artifactId>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-mail</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <artifactId>hutool-all</artifactId>
            <groupId>cn.hutool</groupId>
            <version>5.8.12</version>
        </dependency>
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <artifactId>commons-httpclient</artifactId>
            <groupId>commons-httpclient</groupId>
            <scope>compile</scope>
            <version>3.1</version>
        </dependency>
        <dependency>
            <artifactId>dom4j</artifactId>
            <groupId>dom4j</groupId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>1.6.3</version>
        </dependency>
        <!--腾讯云cos-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.233</version>
        </dependency>
        <!--图片处理工具-->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.14</version>
        </dependency>
        <!-- 支持图片格式 WebP -->
        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>0.1.6</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.netty/netty-all -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.111.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.maxmind.geoip2</groupId>
            <artifactId>geoip2</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <!--单元测试类-->
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-test</artifactId>-->
        <!--<scope>test</scope>-->
        <!--</dependency>-->
        <dependency>
            <groupId>net.sendcloud.sdk</groupId>
            <artifactId>sendcloud-sdk-java</artifactId>
            <version>1.9</version>
        </dependency>
        <!--                <dependency>-->
        <!--                  <groupId>com.taobao.arthas</groupId>-->
        <!--                  <artifactId>arthas-spring-boot-starter</artifactId>-->
        <!--                  <version>3.6.1</version>-->
        <!--                </dependency>-->

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.6</version>
        </dependency>

        <!-- Batik for SVG to PNG conversion -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
            <version>1.14</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-codec</artifactId>
            <version>1.14</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svg-dom</artifactId>
            <version>1.14</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-imaging</artifactId>
            <version>1.0-alpha2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.1-jre</version>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>com.twelvemonkeys.imageio</groupId>-->
        <!--<artifactId>imageio-avif</artifactId>-->
        <!--<version>3.8.1</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos-sts_api</artifactId>
            <version>3.1.0</version>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>org.apache.commons</groupId>-->
        <!--<artifactId>commons-csv</artifactId>-->
        <!--<version>1.9.0</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--<groupId>org.apache.httpcomponents.client5</groupId>-->
        <!--<artifactId>httpclient5</artifactId>-->
        <!--<version>5.2</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
            <version>5.1.2</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.shardingsphere</groupId>-->
        <!--            <artifactId>shardingsphere-sql-parser-mysql</artifactId>-->
        <!--            <version>5.1.2</version>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.39.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>com.stripe</groupId>
            <artifactId>stripe-java</artifactId>
            <version>28.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.spullara.mustache.java</groupId>
            <artifactId>compiler</artifactId>
            <version>0.9.11</version>
        </dependency>
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
            <version>2.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.turo</groupId>
            <artifactId>pushy</artifactId>
            <version>0.13.10</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>
    <description>piclumen</description>
    <groupId>com.lx</groupId>
    <modelVersion>4.0.0</modelVersion>
    <name>piclumen</name>
    <parent>
        <artifactId>spring-boot-starter-parent</artifactId>
        <groupId>org.springframework.boot</groupId>
        <relativePath/>
        <version>2.7.0</version> <!-- lookup parent from repository -->
    </parent>
    <properties>
        <java.version>11</java.version>
    </properties>


    <version>0.0.1-SNAPSHOT</version>

</project>
